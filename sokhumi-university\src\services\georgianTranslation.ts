/**
 * Georgian Translation Service
 * High-quality local translation for Georgian ↔ English
 */

// Georgian Unicode ranges
const GEORGIAN_RANGES = {
  // Georgian (Mkhedruli) - main modern script
  mkhedruli: /[\u10A0-\u10FF]/,
  // Georgian (Asomtavruli) - ancient script
  asomtavruli: /[\u10A0-\u10C5]/,
  // Georgian (Nuskhuri) - ecclesiastical script  
  nuskhuri: /[\u2D00-\u2D25]/,
  // Georgian Extended
  extended: /[\u1C90-\u1CBF]/
};

// Common university terms dictionary
const GEORGIAN_DICTIONARY: Record<string, string> = {
  // University terms
  'უნივერსიტეტი': 'university',
  'განათლება': 'education',
  'კვლევა': 'research',
  'ინოვაცია': 'innovation',
  'სტუდენტი': 'student',
  'სტუდენტები': 'students',
  'ლექტორი': 'lecturer',
  'პროფესორი': 'professor',
  'ფაკულტეტი': 'faculty',
  'კურსი': 'course',
  'კურსები': 'courses',
  'ლექცია': 'lecture',
  'ლექციები': 'lectures',
  'სემინარი': 'seminar',
  'ლაბორატორია': 'laboratory',
  'ბიბლიოთეკა': 'library',
  'ხარისხი': 'degree',
  'დიპლომი': 'diploma',
  'ცერტიფიკატი': 'certificate',
  'მაგისტრი': 'master',
  'დოქტორი': 'doctor',
  'ბაკალავრი': 'bachelor',
  'ასპირანტი': 'graduate student',
  'დოქტორანტი': 'doctoral student',
  'გამოცდა': 'exam',
  'გამოცდები': 'exams',
  'შეფასება': 'assessment',
  'ქულა': 'grade',
  'ქულები': 'grades',
  'სემესტრი': 'semester',
  'წელი': 'year',
  'კურიკულუმი': 'curriculum',
  'პროგრამა': 'program',
  'პროგრამები': 'programs',
  'სპეციალობა': 'specialization',
  'მიმართულება': 'direction',
  'კვლევითი': 'research',
  'აკადემიური': 'academic',
  'სამეცნიერო': 'scientific',
  'ინფორმაცია': 'information',
  'დახმარება': 'help',
  'კითხვა': 'question',
  'კითხვები': 'questions',
  'პასუხი': 'answer',
  'პასუხები': 'answers',
  'რეგისტრაცია': 'registration',
  'ჩარიცხვა': 'enrollment',
  'განაცხადი': 'application',
  'მოთხოვნები': 'requirements',
  'ვადები': 'deadlines',
  'ღირებულება': 'cost',
  'ფასი': 'price',
  'სტიპენდია': 'scholarship',
  'დაფინანსება': 'funding',
  'კონტაქტი': 'contact',
  'მისამართი': 'address',
  'ტელეფონი': 'phone',
  'ელფოსტა': 'email',
  'ვებსაიტი': 'website',
  'ონლაინ': 'online',
  'დისტანციური': 'distance',
  'პრეზენცია': 'attendance',
  'ჯგუფი': 'group',
  'ჯგუფები': 'groups',
  'კლასი': 'class',
  'კლასები': 'classes',
  'აუდიტორია': 'auditorium',
  'ოთახი': 'room',
  'კამპუსი': 'campus',
  'შენობა': 'building',
  'ადგილი': 'location',
  'დრო': 'time',
  'განრიგი': 'schedule',
  'კალენდარი': 'calendar',
  'თარიღი': 'date',
  'საათი': 'hour',
  'წუთი': 'minute',
  'დღე': 'day',
  'კვირა': 'week',
  'თვე': 'month',
  'ახალი': 'new',
  'ძველი': 'old',
  'მნიშვნელოვანი': 'important',
  'საჭირო': 'necessary',
  'შესაძლებელი': 'possible',
  'ხელმისაწვდომი': 'available',
  'უფასო': 'free',
  'ღია': 'open',
  'დახურული': 'closed',
  'მუშაობს': 'working',
  'არ მუშაობს': 'not working',
  'ყველა': 'all',
  'ზოგიერთი': 'some',
  'არცერთი': 'none',
  'პირველი': 'first',
  'ბოლო': 'last',
  'შუა': 'middle',
  'დასაწყისი': 'beginning',
  'ბოლო': 'end',
  'მეტი': 'more',
  'ნაკლები': 'less',
  'იგივე': 'same',
  'განსხვავებული': 'different',
  'ახალი': 'new',
  'ძველი': 'old',
  'კარგი': 'good',
  'ცუდი': 'bad',
  'საუკეთესო': 'best',
  'ყველაზე ცუდი': 'worst',
  'დიდი': 'big',
  'პატარა': 'small',
  'გრძელი': 'long',
  'მოკლე': 'short',
  'მაღალი': 'high',
  'დაბალი': 'low',
  'ღრმა': 'deep',
  'ზედაპირული': 'shallow',
  'ფართო': 'wide',
  'ვიწრო': 'narrow',
  'სწრაფი': 'fast',
  'ნელი': 'slow',
  'ადრე': 'early',
  'გვიან': 'late',
  'ახლა': 'now',
  'მერე': 'later',
  'ადრე': 'before',
  'შემდეგ': 'after',
  'დღეს': 'today',
  'ხვალ': 'tomorrow',
  'გუშინ': 'yesterday',
  'ამ კვირაში': 'this week',
  'მომავალ კვირაში': 'next week',
  'წინა კვირაში': 'last week',
  'ამ თვეში': 'this month',
  'მომავალ თვეში': 'next month',
  'წინა თვეში': 'last month',
  'ამ წელს': 'this year',
  'მომავალ წელს': 'next year',
  'წინა წელს': 'last year'
};

// English to Georgian reverse dictionary
const ENGLISH_DICTIONARY: Record<string, string> = Object.fromEntries(
  Object.entries(GEORGIAN_DICTIONARY).map(([ka, en]) => [en, ka])
);

export class GeorgianTranslationService {
  
  /**
   * Detect if text contains Georgian characters
   */
  static detectLanguage(text: string): 'ka' | 'en' {
    if (!text || text.trim().length === 0) return 'en';
    
    // Check for Georgian Unicode characters
    const hasGeorgian = GEORGIAN_RANGES.mkhedruli.test(text) ||
                       GEORGIAN_RANGES.asomtavruli.test(text) ||
                       GEORGIAN_RANGES.nuskhuri.test(text) ||
                       GEORGIAN_RANGES.extended.test(text);
    
    return hasGeorgian ? 'ka' : 'en';
  }

  /**
   * Get language confidence score
   */
  static getLanguageConfidence(text: string): number {
    const totalChars = text.length;
    if (totalChars === 0) return 0;
    
    const georgianChars = (text.match(/[\u10A0-\u10FF\u2D00-\u2D25\u1C90-\u1CBF]/g) || []).length;
    return georgianChars / totalChars;
  }

  /**
   * Simple Georgian to English translation using dictionary
   */
  static async translateToEnglish(georgianText: string): Promise<{
    translatedText: string;
    confidence: number;
    method: 'dictionary' | 'fallback';
  }> {
    if (!georgianText || georgianText.trim().length === 0) {
      return {
        translatedText: '',
        confidence: 1.0,
        method: 'dictionary'
      };
    }

    // Simple dictionary-based translation
    let translatedText = georgianText;
    let matchCount = 0;
    const words = georgianText.toLowerCase().split(/\s+/);
    
    for (const [georgian, english] of Object.entries(GEORGIAN_DICTIONARY)) {
      if (georgianText.toLowerCase().includes(georgian)) {
        translatedText = translatedText.replace(new RegExp(georgian, 'gi'), english);
        matchCount++;
      }
    }

    const confidence = words.length > 0 ? matchCount / words.length : 0;

    return {
      translatedText: translatedText || georgianText,
      confidence,
      method: 'dictionary'
    };
  }

  /**
   * Simple English to Georgian translation using dictionary
   */
  static async translateToGeorgian(englishText: string): Promise<{
    translatedText: string;
    confidence: number;
    method: 'dictionary' | 'fallback';
  }> {
    if (!englishText || englishText.trim().length === 0) {
      return {
        translatedText: '',
        confidence: 1.0,
        method: 'dictionary'
      };
    }

    // Simple dictionary-based translation
    let translatedText = englishText;
    let matchCount = 0;
    const words = englishText.toLowerCase().split(/\s+/);
    
    for (const [english, georgian] of Object.entries(ENGLISH_DICTIONARY)) {
      if (englishText.toLowerCase().includes(english)) {
        translatedText = translatedText.replace(new RegExp(english, 'gi'), georgian);
        matchCount++;
      }
    }

    const confidence = words.length > 0 ? matchCount / words.length : 0;

    return {
      translatedText: translatedText || englishText,
      confidence,
      method: 'dictionary'
    };
  }

  /**
   * Check if translation is needed
   */
  static needsTranslation(text: string, targetLanguage: 'ka' | 'en'): boolean {
    const detectedLanguage = this.detectLanguage(text);
    return detectedLanguage !== targetLanguage;
  }

  /**
   * Get supported languages
   */
  static getSupportedLanguages(): string[] {
    return ['ka', 'en'];
  }

  /**
   * Validate Georgian text
   */
  static isValidGeorgian(text: string): boolean {
    if (!text || text.trim().length === 0) return false;
    return this.getLanguageConfidence(text) > 0.1;
  }

  /**
   * Clean and normalize Georgian text
   */
  static normalizeGeorgian(text: string): string {
    if (!text) return '';
    
    // Remove extra whitespace
    return text.trim().replace(/\s+/g, ' ');
  }

  /**
   * Get translation statistics
   */
  static getTranslationStats(originalText: string, translatedText: string): {
    originalLength: number;
    translatedLength: number;
    compressionRatio: number;
    hasGeorgian: boolean;
  } {
    return {
      originalLength: originalText.length,
      translatedLength: translatedText.length,
      compressionRatio: originalText.length > 0 ? translatedText.length / originalText.length : 0,
      hasGeorgian: this.detectLanguage(originalText) === 'ka'
    };
  }
}
