import { useState, useEffect } from 'react';
import Chat from './components/Chat';
import ProductCard from './components/ProductCard';
import ProductRecommendationOverlay from './components/ProductRecommendationOverlay';
import CartIcon from './components/Cart/CartIcon';
import { OverlayProvider } from './context/OverlayContext';
import { UserPreferencesProvider } from './context/UserPreferencesContext';
import { CartProvider } from './context/CartContext';
import { allProducts, getFeaturedProducts, getProductsByCategory } from './data/products';
import type { Product } from './types/product';
import './App.css';

function App() {
  // State for products
  const [featuredProducts] = useState<Product[]>(getFeaturedProducts(4));
  const [displayedProducts, setDisplayedProducts] = useState<Product[]>(allProducts.slice(0, 8));
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [categories, setCategories] = useState<string[]>(['All']);

  // Extract unique categories from products
  useEffect(() => {
    const uniqueCategories = ['All', ...new Set(allProducts.map(product => product.category))];
    setCategories(uniqueCategories);
  }, []);

  // Filter products by category
  useEffect(() => {
    if (selectedCategory === 'All') {
      setDisplayedProducts(allProducts.slice(0, 8));
    } else {
      const filtered = getProductsByCategory(selectedCategory);
      setDisplayedProducts(filtered.slice(0, 8));
    }
  }, [selectedCategory]);

  // Chat configuration with dark theme
  const chatConfig = {
    theme: {
      primaryColor: '#7c5cff',
      secondaryColor: '#2a2a3a',
      textColor: '#e0e0e0',
      backgroundColor: '#1e1e2d',
      position: 'right' as const,
      initialState: 'minimized' as const,
      greeting: 'Hi! I\'m CHASTER. What products are you looking for today?',
    },
    agentName: 'CHASTER',
    agentAvatar: '/icon.png', // Local CHASTER AI logo
    enableSound: true,
    enableNotifications: true,
    enableAttachments: true,
    enableHistory: true,
  };

  return (
    <CartProvider>
      <UserPreferencesProvider>
        <OverlayProvider>
        <div className="app">
          {/* Header */}
          <header className="header">
            <div className="container">
              <div className="logo">
                <h1>Premium Store</h1>
              </div>
              <nav className="nav">
                <ul>
                  <li><a href="#" className="active">Home</a></li>
                  <li><a href="#">Products</a></li>
                  <li><a href="#">Collections</a></li>
                  <li><a href="#">About</a></li>
                  <li><a href="#">Contact</a></li>
                </ul>
              </nav>
              <div className="header-actions">
                <button className="icon-button">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
                <CartIcon />
                <button className="icon-button">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </header>

        {/* Hero Section */}
        <section className="hero">
          <div className="container">
            <div className="hero-content">
              <h2>Discover <span>Premium Quality</span> Products</h2>
              <p>Experience the finest collection of carefully curated products designed for those who appreciate excellence and sophistication.</p>
              <button className="btn-primary">Explore Collection</button>
            </div>
          </div>
        </section>

        {/* Featured Products */}
        <section className="featured-products">
          <div className="container">
            <h2 className="section-title">Featured Products</h2>
            <div className="product-grid">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        </section>

        {/* All Products */}
        <section className="featured-products">
          <div className="container">
            <h2 className="section-title">Our Products</h2>

            {/* Category Filter */}
            <div className="category-filter">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category)}
                >
                  {category}
                </button>
              ))}
            </div>

            <div className="product-grid">
              {displayedProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* Show load more button only if there are more products to load */}
            {(selectedCategory === 'All' && displayedProducts.length < allProducts.length) ||
             (selectedCategory !== 'All' && displayedProducts.length < getProductsByCategory(selectedCategory).length) ? (
              <div className="load-more">
                <button
                  className="btn-secondary"
                  onClick={() => {
                    if (selectedCategory === 'All') {
                      setDisplayedProducts(allProducts.slice(0, displayedProducts.length + 4));
                    } else {
                      const filtered = getProductsByCategory(selectedCategory);
                      setDisplayedProducts(filtered.slice(0, displayedProducts.length + 4));
                    }
                  }}
                >
                  Load More Products
                </button>
              </div>
            ) : null}
          </div>
        </section>

        {/* Footer */}
        <footer className="footer">
          <div className="container">
            <div className="footer-content">
              <div className="footer-logo">
                <h2>Premium Store</h2>
                <p>Quality products for discerning customers.</p>
              </div>
              <div className="footer-links">
                <div className="footer-column">
                  <h3>Shop</h3>
                  <ul>
                    <li><a href="#">New Arrivals</a></li>
                    <li><a href="#">Best Sellers</a></li>
                    <li><a href="#">Sale</a></li>
                    <li><a href="#">Collections</a></li>
                  </ul>
                </div>
                <div className="footer-column">
                  <h3>Help</h3>
                  <ul>
                    <li><a href="#">FAQ</a></li>
                    <li><a href="#">Shipping</a></li>
                    <li><a href="#">Returns</a></li>
                    <li><a href="#">Contact Us</a></li>
                  </ul>
                </div>
                <div className="footer-column">
                  <h3>About</h3>
                  <ul>
                    <li><a href="#">Our Story</a></li>
                    <li><a href="#">Sustainability</a></li>
                    <li><a href="#">Careers</a></li>
                    <li><a href="#">Press</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="footer-bottom">
              <p>&copy; 2025 Premium Store. All rights reserved.</p>
            </div>
          </div>
        </footer>

        {/* Product Recommendation Overlay */}
        <ProductRecommendationOverlay />

        {/* Chat Component */}
        <Chat config={chatConfig} />
      </div>
        </OverlayProvider>
      </UserPreferencesProvider>
    </CartProvider>
  );
}

export default App;
