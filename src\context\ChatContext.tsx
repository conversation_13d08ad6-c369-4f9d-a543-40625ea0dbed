import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { ReactNode, FC } from 'react';
import { v4 as uuidv4 } from 'uuid';
import type { ChatState, Message, ChatConfig, MessageStatus } from '../types/chat';
import { loadMessages, saveMessages, clearMessages, loadLastReadTimestamp, saveLastReadTimestamp } from '../utils/storage';
import {
  getGroqResponse,
  formatChatHistoryForGroq,
  parseRecommendationCommand,
  getProductsByIds
} from '../services/groqService';
import { allProducts } from '../data/products';
import { useOverlay } from './OverlayContext';
import { useUserPreferences } from './UserPreferencesContext';
import { useCart } from './CartContext';
import { GeorgianTranslationService } from '../services/georgianTranslation';

// Define the context type
interface ChatContextType {
  state: ChatState;
  config: ChatConfig;
  sendMessage: (content: string) => void;
  sendImage: (file: File) => void;
  toggleChat: () => void;
  markAllAsRead: () => void;
  setTyping: (isTyping: boolean) => void;
  clearHistory: () => void;
  updateConfig: (newConfig: Partial<ChatConfig>) => void;
  setDraftMessage: (message: string) => void;
}

// Create the context with a default value
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Define action types
type ChatAction =
  | { type: 'TOGGLE_CHAT' }
  | { type: 'SEND_MESSAGE'; payload: Message }
  | { type: 'RECEIVE_MESSAGE'; payload: Message }
  | { type: 'UPDATE_MESSAGE_STATUS'; payload: { id: string; status: MessageStatus } }
  | { type: 'MARK_ALL_READ' }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'CLEAR_HISTORY' }
  | { type: 'LOAD_HISTORY'; payload: Message[] }
  | { type: 'SET_LAST_READ'; payload: number }
  | { type: 'SET_DRAFT_MESSAGE'; payload: string };

// Define the initial state
const initialState: ChatState = {
  isOpen: false,
  messages: [],
  unreadCount: 0,
  isTyping: false,
  lastReadTimestamp: null,
  draftMessage: '',
};

// Default configuration
const defaultConfig: ChatConfig = {
  theme: {
    primaryColor: '#4a6cf7',
    secondaryColor: '#f0f4ff',
    textColor: '#333333',
    backgroundColor: '#ffffff',
    position: 'right',
    initialState: 'minimized',
    greeting: 'Hello! How can I help you today?',
  },
  agentName: 'Support Agent',
  enableSound: true,
  enableNotifications: true,
  enableAttachments: false,
  enableHistory: true,
};

// Reducer function
const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'TOGGLE_CHAT':
      return {
        ...state,
        isOpen: !state.isOpen,
      };
    case 'SEND_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };
    case 'RECEIVE_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
        unreadCount: state.isOpen ? state.unreadCount : state.unreadCount + 1,
      };
    case 'UPDATE_MESSAGE_STATUS':
      return {
        ...state,
        messages: state.messages.map((message) =>
          message.id === action.payload.id
            ? { ...message, status: action.payload.status }
            : message
        ),
      };
    case 'MARK_ALL_READ':
      return {
        ...state,
        unreadCount: 0,
        messages: state.messages.map((message) => ({
          ...message,
          isRead: true,
        })),
      };
    case 'SET_TYPING':
      return {
        ...state,
        isTyping: action.payload,
      };
    case 'CLEAR_HISTORY':
      return {
        ...state,
        messages: [],
        unreadCount: 0,
      };
    case 'LOAD_HISTORY':
      return {
        ...state,
        messages: action.payload,
        unreadCount: action.payload.filter(
          (message) => !message.isRead && message.sender === 'agent'
        ).length,
      };
    case 'SET_LAST_READ':
      return {
        ...state,
        lastReadTimestamp: action.payload,
      };
    case 'SET_DRAFT_MESSAGE':
      return {
        ...state,
        draftMessage: action.payload,
      };
    default:
      return state;
  }
};

// Provider component
interface ChatProviderProps {
  children: ReactNode;
  config?: Partial<ChatConfig>;
}

export const ChatProvider: FC<ChatProviderProps> = ({
  children,
  config: userConfig,
}) => {
  const [config, setConfig] = React.useState<ChatConfig>({
    ...defaultConfig,
    ...userConfig,
  });

  const [state, dispatch] = useReducer(chatReducer, initialState);

  // Get the overlay context
  const overlay = useOverlay();

  // Get the user preferences context
  const userPreferences = useUserPreferences();

  // Get the cart context
  const cart = useCart();

  // Load chat history from local storage on mount
  useEffect(() => {
    if (config.enableHistory) {
      const savedMessages = loadMessages();
      if (savedMessages.length > 0) {
        dispatch({ type: 'LOAD_HISTORY', payload: savedMessages });
      } else if (config.theme.greeting) {
        // Add greeting message if no history exists
        const greetingMessage: Message = {
          id: uuidv4(),
          content: config.theme.greeting,
          sender: 'agent',
          timestamp: Date.now(),
          status: 'delivered',
          isRead: false,
        };
        dispatch({ type: 'RECEIVE_MESSAGE', payload: greetingMessage });
      }

      const lastRead = loadLastReadTimestamp();
      if (lastRead) {
        dispatch({ type: 'SET_LAST_READ', payload: lastRead });
      }
    }
  }, [config.enableHistory, config.theme.greeting]);

  // Save messages to local storage when they change
  useEffect(() => {
    if (config.enableHistory && state.messages.length > 0) {
      saveMessages(state.messages);
    }
  }, [state.messages, config.enableHistory]);

  // Send a message
  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Detect language and translate if Georgian
    const isGeorgian = GeorgianTranslationService.detectLanguage(content) === 'ka';
    let translatedContent = content;

    if (isGeorgian) {
      console.log('🇬🇪 Georgian text detected, translating to English for AI...');
      const translationResult = await GeorgianTranslationService.translateToEnglish(content);
      translatedContent = translationResult.translatedText;
      console.log('🔄 Translation:', content, '→', translatedContent);
    }

    // Create and send user message (show original language to user)
    const userMessage: Message = {
      id: uuidv4(),
      content, // Show original language in chat
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending',
      isRead: true,
    };

    dispatch({ type: 'SEND_MESSAGE', payload: userMessage });

    // Update message status to sent
    setTimeout(() => {
      dispatch({
        type: 'UPDATE_MESSAGE_STATUS',
        payload: { id: userMessage.id, status: 'sent' },
      });

      setTimeout(() => {
        dispatch({
          type: 'UPDATE_MESSAGE_STATUS',
          payload: { id: userMessage.id, status: 'delivered' },
        });
      }, 500);
    }, 300);

    // Show typing indicator while waiting for AI response
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Format chat history for the API
      const chatHistory = formatChatHistoryForGroq(state.messages.slice(-10)); // Use last 10 messages for context

      // Track user search query for context (use original content)
      userPreferences.addRecentSearch(content);

      // Enhanced context for better conversation flow with REAL-TIME cart state
      console.log('🛒 Real-time cart state being passed to AI:', {
        itemCount: cart.items.length,
        items: cart.items.map(item => ({ id: item.productId, name: item.name, quantity: item.quantity })),
        totalPrice: cart.totalPrice
      });

      const enhancedContext = {
        ...userPreferences.userPreferences,
        recentSearches: userPreferences.previousQueries.slice(-5), // Last 5 searches for context
        conversationFlow: chatHistory.slice(-3), // Last 3 messages for immediate context
        cartItems: cart.items, // REAL-TIME cart items
        lastProductSearch: userPreferences.previousQueries.find(q =>
          q.toLowerCase().includes('phone') ||
          q.toLowerCase().includes('smartphone') ||
          q.toLowerCase().includes('laptop')
        )
      };

      // Get response from Groq AI with enhanced context and REAL-TIME cart data
      // Use translated content for AI, but preserve original for context
      const aiResponse = await getGroqResponse(
        translatedContent, // Send translated English to AI
        chatHistory,
        enhancedContext,
        userPreferences.previousQueries,
        cart.items // Pass REAL-TIME cart items directly
      );

      // Parse the response for recommendation commands, sorting requests, product selections, and cart commands
      console.log('🤖 Raw AI Response:', aiResponse);

      const {
        cleanedResponse,
        productIds,
        title,
        sortRequest,
        selectedProductId,
        selectionContext,
        addToCartProductId,
        removeFromCartProductId
      } = parseRecommendationCommand(aiResponse);

      // Enhanced debugging for overlay trigger
      console.log('🔍 Parsed Commands:', {
        productIds,
        title,
        hasProductIds: productIds && productIds.length > 0,
        addToCartProductId,
        removeFromCartProductId,
        cleanedResponse: cleanedResponse.substring(0, 100) + '...'
      });

      // Enhanced debugging for cart commands
      if (addToCartProductId) {
        console.log('🛒 CART ADD COMMAND DETECTED:', addToCartProductId);
      } else {
        console.log('🛒 NO CART ADD COMMAND in AI response');
        // Check if AI mentioned adding to cart without using command
        if (aiResponse.toLowerCase().includes('add') && aiResponse.toLowerCase().includes('cart')) {
          console.log('⚠️ AI mentioned cart but no [ADD_TO_CART: ...] command found!');
          console.log('⚠️ AI response:', aiResponse);
        }
      }

      // Log parsed commands for debugging
      if (addToCartProductId) {
        console.log('🛒 ADD command parsed:', addToCartProductId);
      }
      if (removeFromCartProductId) {
        console.log('🛒 REMOVE command parsed:', removeFromCartProductId);
      }

      // Handle cart commands with enhanced error handling and feedback
      if (addToCartProductId) {
        try {
          console.log(`CHASTER attempting to add product: "${addToCartProductId}"`);
          console.log('Current cart items before addition:', cart.items.map(item => ({ id: item.productId, name: item.name })));

          // Enhanced product ID matching - try multiple approaches
          let productIdToAdd = addToCartProductId;
          let productToAdd = allProducts.find(p => p.id === productIdToAdd);

          // If no exact match, try to find by product name (case-insensitive)
          if (!productToAdd) {
            productToAdd = allProducts.find(p =>
              p.name.toLowerCase().includes(addToCartProductId.toLowerCase()) ||
              addToCartProductId.toLowerCase().includes(p.name.toLowerCase())
            );
            if (productToAdd) {
              productIdToAdd = productToAdd.id;
              console.log(`Found product by name match: ${productToAdd.name} (${productIdToAdd})`);
            }
          }

          // If still no match, try partial ID matching
          if (!productToAdd) {
            productToAdd = allProducts.find(p =>
              p.id.includes(addToCartProductId) ||
              addToCartProductId.includes(p.id)
            );
            if (productToAdd) {
              productIdToAdd = productToAdd.id;
              console.log(`Found product by partial ID match: ${productToAdd.name} (${productIdToAdd})`);
            }
          }

          if (productToAdd) {
            console.log(`Adding product: ${productToAdd.name} (${productIdToAdd})`);
            const success = await cart.addItem(productIdToAdd, 1);
            if (success) {
              console.log(`✅ Successfully added product ${productIdToAdd} to cart`);
              // Remember the cart action
              userPreferences.addRecentSearch(`Added ${productToAdd.name} to cart`);

              // Add a follow-up message to confirm addition
              const confirmationMessage: Message = {
                id: uuidv4(),
                content: `✅ Added ${productToAdd.name} to your cart successfully!`,
                sender: 'agent',
                timestamp: Date.now(),
                status: 'delivered',
                isRead: state.isOpen
              };

              // Add confirmation message after a short delay
              setTimeout(() => {
                dispatch({ type: 'RECEIVE_MESSAGE', payload: confirmationMessage });
              }, 500);

            } else {
              console.error(`❌ Failed to add product ${productIdToAdd} to cart`);

              // Add error message
              const errorMessage: Message = {
                id: uuidv4(),
                content: `❌ Sorry, I couldn't add ${productToAdd.name} to your cart. It might be out of stock or there was a technical issue. Please try again.`,
                sender: 'agent',
                timestamp: Date.now(),
                status: 'delivered',
                isRead: state.isOpen
              };

              setTimeout(() => {
                dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
              }, 500);
            }
          } else {
            console.error(`❌ Product "${addToCartProductId}" not found in available products`);
            console.log('Available products (first 10):', allProducts.slice(0, 10).map(p => `${p.id}: ${p.name}`));

            // Add helpful error message
            const notFoundMessage: Message = {
              id: uuidv4(),
              content: `❌ I couldn't find "${addToCartProductId}" in our product catalog. Please try searching for products first, then ask me to add specific items to your cart.`,
              sender: 'agent',
              timestamp: Date.now(),
              status: 'delivered',
              isRead: state.isOpen
            };

            setTimeout(() => {
              dispatch({ type: 'RECEIVE_MESSAGE', payload: notFoundMessage });
            }, 500);
          }
        } catch (error) {
          console.error('Error adding product to cart:', error);

          // Add technical error message
          const techErrorMessage: Message = {
            id: uuidv4(),
            content: `❌ There was a technical error adding the item to your cart. Please try again or add it manually by clicking the product.`,
            sender: 'agent',
            timestamp: Date.now(),
            status: 'delivered',
            isRead: state.isOpen
          };

          setTimeout(() => {
            dispatch({ type: 'RECEIVE_MESSAGE', payload: techErrorMessage });
          }, 500);
        }
      }

      if (removeFromCartProductId) {
        try {
          console.log(`CHASTER attempting to remove product(s): "${removeFromCartProductId}"`);
          console.log('Current cart items:', cart.items.map(item => ({ id: item.productId, name: item.name })));

          // Check if this is a multiple item removal (comma-separated IDs)
          const productIdsToRemove = removeFromCartProductId.includes(',')
            ? removeFromCartProductId.split(',').map(id => id.trim())
            : [removeFromCartProductId.trim()];

          console.log('Product IDs to remove:', productIdsToRemove);

          if (productIdsToRemove.length > 1) {
            // Handle multiple item removal
            const itemsToRemove = [];
            const validProductIds = [];

            for (const productId of productIdsToRemove) {
              // Enhanced product ID matching for each item
              let actualProductId = productId;
              let cartItem = cart.items.find(item => item.productId === actualProductId);

              // If no exact match, try name-based matching
              if (!cartItem) {
                cartItem = cart.items.find(item =>
                  item.name.toLowerCase().includes(productId.toLowerCase()) ||
                  productId.toLowerCase().includes(item.name.toLowerCase())
                );
                if (cartItem) {
                  actualProductId = cartItem.productId;
                  console.log(`Found by name match: ${cartItem.name} (${actualProductId})`);
                }
              }

              if (cartItem) {
                itemsToRemove.push(cartItem);
                validProductIds.push(actualProductId);
              } else {
                console.warn(`❌ Product "${productId}" not found in cart`);
              }
            }

            if (validProductIds.length > 0) {
              console.log(`Removing ${validProductIds.length} items from cart:`, itemsToRemove.map(item => item.name));
              const success = await cart.removeMultipleItems(validProductIds);

              if (success) {
                console.log(`✅ Successfully removed ${validProductIds.length} items from cart`);

                // Add confirmation message for multiple items
                const itemNames = itemsToRemove.map(item => item.name).join(', ');
                const confirmationMessage: Message = {
                  id: uuidv4(),
                  content: `✅ Removed ${validProductIds.length} items from your cart: ${itemNames}`,
                  sender: 'agent',
                  timestamp: Date.now(),
                  status: 'delivered',
                  isRead: state.isOpen
                };

                setTimeout(() => {
                  dispatch({ type: 'RECEIVE_MESSAGE', payload: confirmationMessage });
                }, 500);

                // Track the removal action
                userPreferences.addRecentSearch(`Removed ${validProductIds.length} items from cart`);
              } else {
                console.error(`❌ Failed to remove multiple items from cart`);
              }
            }
          } else {
            // Handle single item removal (existing logic)
            let productIdToRemove = productIdsToRemove[0];
            let cartItem = cart.items.find(item => item.productId === productIdToRemove);

            // If no exact match, try to find by product name (case-insensitive)
            if (!cartItem) {
              const productByName = allProducts.find(p =>
                p.name.toLowerCase().includes(removeFromCartProductId.toLowerCase()) ||
                removeFromCartProductId.toLowerCase().includes(p.name.toLowerCase())
              );
              if (productByName) {
                productIdToRemove = productByName.id;
                cartItem = cart.items.find(item => item.productId === productIdToRemove);
                console.log(`Found product by name match: ${productByName.name} (${productIdToRemove})`);
              }
            }

            // If still no match, try partial ID matching
            if (!cartItem) {
              cartItem = cart.items.find(item =>
                item.productId.includes(removeFromCartProductId) ||
                removeFromCartProductId.includes(item.productId)
              );
              if (cartItem) {
                productIdToRemove = cartItem.productId;
                console.log(`Found product by partial ID match: ${cartItem.name} (${productIdToRemove})`);
              }
            }

            if (cartItem) {
              console.log(`Removing product: ${cartItem.name} (${productIdToRemove})`);
              const success = await cart.removeItem(productIdToRemove);
              if (success) {
                console.log(`✅ Successfully removed product ${productIdToRemove} from cart`);
                // Remember the cart action
                userPreferences.addRecentSearch(`Removed ${cartItem.name} from cart`);

                // Add a follow-up message to confirm removal
                const confirmationMessage: Message = {
                  id: uuidv4(),
                  content: `✅ Removed ${cartItem.name} from your cart successfully!`,
                  sender: 'agent',
                  timestamp: Date.now(),
                  status: 'delivered',
                  isRead: state.isOpen
                };

                // Add confirmation message after a short delay
                setTimeout(() => {
                  dispatch({ type: 'RECEIVE_MESSAGE', payload: confirmationMessage });
                }, 500);

              } else {
                console.error(`❌ Failed to remove product ${productIdToRemove} from cart`);

                // Add error message
                const errorMessage: Message = {
                  id: uuidv4(),
                  content: `❌ Sorry, I couldn't remove ${cartItem.name} from your cart. Please try again or remove it manually.`,
                  sender: 'agent',
                  timestamp: Date.now(),
                  status: 'delivered',
                  isRead: state.isOpen
                };

                setTimeout(() => {
                  dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
                }, 500);
              }
            } else {
              console.error(`❌ Product "${removeFromCartProductId}" not found in cart`);
              console.log('Available cart items:', cart.items.map(item => `${item.name} (${item.productId})`));

              // Add helpful error message
              const notFoundMessage: Message = {
                id: uuidv4(),
                content: `❌ I couldn't find "${removeFromCartProductId}" in your cart. ${cart.items.length > 0 ? `Your cart contains: ${cart.items.map(item => item.name).join(', ')}` : 'Your cart is empty.'}`,
                sender: 'agent',
                timestamp: Date.now(),
                status: 'delivered',
                isRead: state.isOpen
              };

              setTimeout(() => {
                dispatch({ type: 'RECEIVE_MESSAGE', payload: notFoundMessage });
              }, 500);
            }
          }
        } catch (error) {
          console.error('Error removing product from cart:', error);
        }
      }

      // If a product was selected, track it in user preferences
      if (selectedProductId && selectionContext) {
        userPreferences.addSelectedProduct(selectedProductId, selectionContext);
        console.log(`User selected product: ${selectedProductId} - Context: ${selectionContext}`);
      }

      // If product recommendations are found, show them in the overlay
      if (productIds && productIds.length > 0) {
        console.log('🎯 TRIGGERING OVERLAY - Product IDs found:', productIds);

        const recommendedProducts = getProductsByIds(productIds);
        console.log('🎯 Retrieved products for overlay:', recommendedProducts.map(p => ({ id: p.id, name: p.name })));

        const recommendationTitle = title || 'Recommended Products for You';
        console.log('🎯 Overlay title:', recommendationTitle);

        // Track viewed products in user preferences
        recommendedProducts.forEach(product => {
          userPreferences.addViewedProduct(product.id);
        });

        // Show the recommendations in the overlay
        console.log('🎯 Calling overlay.showProductRecommendations...');
        overlay.showProductRecommendations(recommendedProducts, recommendationTitle);
        console.log('🎯 Overlay should now be visible!');

        // If there's a sort request, apply it
        if (sortRequest) {
          overlay.setSorting(sortRequest.criteria, sortRequest.order);
        }

        // Update user preferences with product categories
        const categories = [...new Set(recommendedProducts.map(p => p.category))];
        if (categories.length > 0) {
          userPreferences.updatePreferredCategories([
            ...userPreferences.userPreferences.preferredCategories,
            ...categories
          ].filter((v, i, a) => a.indexOf(v) === i).slice(0, 5)); // Keep unique, limit to 5
        }

        // Extract features from product descriptions and add to preferences
        const features = extractFeaturesFromProducts(recommendedProducts);
        features.forEach(feature => {
          userPreferences.addPreferredFeature(feature);
        });
      } else {
        console.log('❌ OVERLAY NOT TRIGGERED - No product IDs found in AI response');
        console.log('❌ Parsed productIds:', productIds);
        console.log('❌ AI response contained:', aiResponse.substring(0, 200) + '...');
      }

      // Translate AI response back to Georgian if user sent Georgian
      let finalResponse = cleanedResponse;
      if (isGeorgian) {
        console.log('🇬🇪 Translating AI response back to Georgian...');
        const responseTranslation = await GeorgianTranslationService.translateToGeorgian(cleanedResponse);
        finalResponse = responseTranslation.translatedText;
        console.log('🔄 Response translation:', cleanedResponse, '→', finalResponse);
      }

      // Create and send AI response message with the translated response
      const aiMessage: Message = {
        id: uuidv4(),
        content: finalResponse, // Use translated response if Georgian was detected
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen, // Mark as read if chat is open
      };

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Add AI message to chat
      dispatch({ type: 'RECEIVE_MESSAGE', payload: aiMessage });

      // Log final cart state after all operations for debugging
      console.log('🛒 Final cart state after AI response:', {
        itemCount: cart.items.length,
        items: cart.items.map(item => ({ id: item.productId, name: item.name, quantity: item.quantity })),
        totalPrice: cart.totalPrice
      });

    } catch (error) {
      console.error('Error getting AI response:', error);

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Send error message (translate if Georgian was detected)
      let errorContent = "I'm sorry, I couldn't process your request. Please try again later.";
      if (isGeorgian) {
        const errorTranslation = await GeorgianTranslationService.translateToGeorgian(errorContent);
        errorContent = errorTranslation.translatedText;
      }

      const errorMessage: Message = {
        id: uuidv4(),
        content: errorContent,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
    }
  };

  // Toggle chat open/closed
  const toggleChat = () => {
    dispatch({ type: 'TOGGLE_CHAT' });
    if (!state.isOpen) {
      // When opening the chat, mark all as read
      dispatch({ type: 'MARK_ALL_READ' });
      saveLastReadTimestamp(Date.now());
    }
  };

  // Mark all messages as read
  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_READ' });
    saveLastReadTimestamp(Date.now());
  };

  // Set typing indicator
  const setTyping = (isTyping: boolean) => {
    dispatch({ type: 'SET_TYPING', payload: isTyping });
  };

  // Clear chat history
  const clearHistory = () => {
    dispatch({ type: 'CLEAR_HISTORY' });

    // Also clear messages from local storage
    if (config.enableHistory) {
      clearMessages();
    }

    // Add greeting message if configured
    if (config.theme.greeting) {
      const greetingMessage: Message = {
        id: uuidv4(),
        content: config.theme.greeting,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: true,
      };
      dispatch({ type: 'RECEIVE_MESSAGE', payload: greetingMessage });
    }
  };

  // Update configuration
  const updateConfig = (newConfig: Partial<ChatConfig>) => {
    setConfig((prevConfig) => ({
      ...prevConfig,
      ...newConfig,
      theme: {
        ...prevConfig.theme,
        ...(newConfig.theme || {}),
      },
    }));
  };

  // Set draft message
  const setDraftMessage = (message: string) => {
    dispatch({ type: 'SET_DRAFT_MESSAGE', payload: message });
  };

  // Send an image
  const sendImage = async (file: File) => {
    // Create a URL for the image
    const imageUrl = URL.createObjectURL(file);

    const newMessage: Message = {
      id: uuidv4(),
      content: '',
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending',
      isRead: true,
      image: {
        url: imageUrl,
        name: file.name,
        type: file.type,
        size: file.size
      }
    };

    dispatch({ type: 'SEND_MESSAGE', payload: newMessage });

    // Update message status
    setTimeout(() => {
      dispatch({
        type: 'UPDATE_MESSAGE_STATUS',
        payload: { id: newMessage.id, status: 'sent' },
      });

      setTimeout(() => {
        dispatch({
          type: 'UPDATE_MESSAGE_STATUS',
          payload: { id: newMessage.id, status: 'delivered' },
        });
      }, 500);
    }, 300);

    // Show typing indicator
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Format chat history for the API
      const chatHistory = formatChatHistoryForGroq(state.messages.slice(-10));

      // Create a message about the image for the AI
      const imagePrompt = `The user has shared an image named "${file.name}" (${(file.size / 1024).toFixed(1)} KB). Please acknowledge the image and offer assistance.`;

      // Get response from Groq AI with user preferences and cart items
      const aiResponse = await getGroqResponse(
        imagePrompt,
        chatHistory,
        userPreferences.userPreferences,
        userPreferences.previousQueries,
        cart.items
      );

      // Parse the response for recommendation commands, sorting requests, product selections, and cart commands
      const {
        cleanedResponse,
        productIds,
        title,
        sortRequest,
        selectedProductId,
        selectionContext,
        addToCartProductId,
        removeFromCartProductId
      } = parseRecommendationCommand(aiResponse);

      // Handle cart commands with enhanced error handling and feedback
      if (addToCartProductId) {
        try {
          console.log(`CHASTER attempting to add product from image: "${addToCartProductId}"`);

          // Enhanced product ID matching - try multiple approaches
          let productIdToAdd = addToCartProductId;
          let productToAdd = allProducts.find(p => p.id === productIdToAdd);

          // If no exact match, try to find by product name (case-insensitive)
          if (!productToAdd) {
            productToAdd = allProducts.find(p =>
              p.name.toLowerCase().includes(addToCartProductId.toLowerCase()) ||
              addToCartProductId.toLowerCase().includes(p.name.toLowerCase())
            );
            if (productToAdd) {
              productIdToAdd = productToAdd.id;
              console.log(`Found product by name match: ${productToAdd.name} (${productIdToAdd})`);
            }
          }

          if (productToAdd) {
            const success = await cart.addItem(productIdToAdd, 1);
            if (success) {
              console.log(`✅ Successfully added product ${productIdToAdd} to cart from image`);
              userPreferences.addRecentSearch(`Added ${productToAdd.name} to cart`);
            } else {
              console.error(`❌ Failed to add product ${productIdToAdd} to cart from image`);
            }
          } else {
            console.error(`❌ Product "${addToCartProductId}" not found in available products (image context)`);
          }
        } catch (error) {
          console.error('Error adding product to cart from image:', error);
        }
      }

      if (removeFromCartProductId) {
        try {
          const success = await cart.removeItem(removeFromCartProductId);
          if (success) {
            console.log(`Removed product ${removeFromCartProductId} from cart`);
            // Remember the cart action
            const product = allProducts.find(p => p.id === removeFromCartProductId);
            if (product) {
              userPreferences.addRecentSearch(`Removed ${product.name} from cart`);
            }
          } else {
            console.error(`Failed to remove product ${removeFromCartProductId} from cart`);
          }
        } catch (error) {
          console.error('Error removing product from cart:', error);
        }
      }

      // If a product was selected, track it in user preferences
      if (selectedProductId && selectionContext) {
        userPreferences.addSelectedProduct(selectedProductId, selectionContext);
        console.log(`User selected product: ${selectedProductId} - Context: ${selectionContext}`);
      }

      // If product recommendations are found, show them in the overlay
      if (productIds && productIds.length > 0) {
        const recommendedProducts = getProductsByIds(productIds);
        const recommendationTitle = title || 'Recommended Products for You';

        // Track viewed products in user preferences
        recommendedProducts.forEach(product => {
          userPreferences.addViewedProduct(product.id);
        });

        // Show the recommendations in the overlay
        overlay.showProductRecommendations(recommendedProducts, recommendationTitle);

        // If there's a sort request, apply it
        if (sortRequest) {
          overlay.setSorting(sortRequest.criteria, sortRequest.order);
        }

        // Update user preferences with product categories
        const categories = [...new Set(recommendedProducts.map(p => p.category))];
        if (categories.length > 0) {
          userPreferences.updatePreferredCategories([
            ...userPreferences.userPreferences.preferredCategories,
            ...categories
          ].filter((v, i, a) => a.indexOf(v) === i).slice(0, 5)); // Keep unique, limit to 5
        }

        // Extract features from product descriptions and add to preferences
        const features = extractFeaturesFromProducts(recommendedProducts);
        features.forEach(feature => {
          userPreferences.addPreferredFeature(feature);
        });
      }

      // Create and send AI response message with the cleaned response
      const aiMessage: Message = {
        id: uuidv4(),
        content: cleanedResponse,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Add AI message to chat
      dispatch({ type: 'RECEIVE_MESSAGE', payload: aiMessage });

    } catch (error) {
      console.error('Error getting AI response for image:', error);

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Send error message
      const errorMessage: Message = {
        id: uuidv4(),
        content: "I've received your image, but I'm having trouble processing it right now. How else can I help you?",
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
    }
  };

  return (
    <ChatContext.Provider
      value={{
        state,
        config,
        sendMessage,
        sendImage,
        toggleChat,
        markAllAsRead,
        setTyping,
        clearHistory,
        updateConfig,
        setDraftMessage,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use the chat context
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

// Helper function to extract features from product descriptions
const extractFeaturesFromProducts = (products: any[]): string[] => {
  const featurePatterns = [
    /\b(wireless|bluetooth|noise[\s-]cancelling|waterproof|water[\s-]resistant)\b/i,
    /\b(fast[\s-]charging|long[\s-]battery|high[\s-]resolution|ultra[\s-]hd|4k|8k)\b/i,
    /\b(touchscreen|lightweight|portable|compact|foldable|adjustable)\b/i,
    /\b(smart|intelligent|automated|programmable|rechargeable)\b/i,
    /\b(premium|luxury|professional|high[\s-]end|top[\s-]quality|durable)\b/i,
    /\b(gaming|rgb|mechanical|optical|ergonomic|comfortable)\b/i
  ];

  const features = new Set<string>();

  products.forEach(product => {
    const text = `${product.name} ${product.description} ${product.tags.join(' ')}`.toLowerCase();

    featurePatterns.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches && matches[1]) {
        features.add(matches[1].toLowerCase());
      }
    });

    // Also add tags as features
    product.tags.forEach((tag: string) => {
      if (tag.length > 3 && !['and', 'the', 'for', 'with'].includes(tag.toLowerCase())) {
        features.add(tag.toLowerCase());
      }
    });
  });

  return Array.from(features).slice(0, 10); // Limit to 10 features
};

export default ChatContext;
