.product-recommendation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(30, 30, 45, 0.85), rgba(0, 0, 0, 0.6));
  backdrop-filter: blur(8px);
  z-index: 9990; /* Below chat window (9999) but above everything else */
  display: flex;
  justify-content: flex-start;
  align-items: center;
  animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 20px;
  box-sizing: border-box;
  pointer-events: auto;
}

.overlay-content {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  border-radius: 24px;
  /* Enhanced sizing for better product browsing experience */
  width: calc(100% - 440px); /* Leave space for chat window (360px) + cart space (80px) */
  max-width: 1400px; /* Increased from 1000px for more products */
  height: 90vh; /* Increased from 85vh for more vertical space */
  max-height: 90vh;
  overflow: hidden;
  padding: 0;
  margin-left: 20px; /* Reduced margin for more space */
  margin-right: 20px; /* Ensure space from right edge */
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: floatIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  flex-direction: column;
}

.overlay-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32px 32px 24px 32px;
  border-bottom: 1px solid rgba(124, 92, 255, 0.1);
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
  backdrop-filter: blur(10px);
  flex-shrink: 0;
}

.overlay-header h2 {
  font-size: 32px;
  font-weight: 800;
  color: #222;
  margin: 0;
  background: linear-gradient(135deg, #4a6cf7, #7c5cff, #9333ea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
  text-shadow: 0 2px 4px rgba(124, 92, 255, 0.1);
}

.close-button {
  position: absolute;
  top: -12px;
  right: -12px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  border: 1px solid rgba(124, 92, 255, 0.2);
  cursor: pointer;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #7c5cff;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow:
    0 8px 16px rgba(124, 92, 255, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  z-index: 9995; /* Higher than overlay but lower than chat */
  backdrop-filter: blur(10px);
}

.close-button:hover {
  background: linear-gradient(135deg, #7c5cff, #9333ea);
  color: white;
  transform: scale(1.08) rotate(90deg);
  box-shadow:
    0 12px 24px rgba(124, 92, 255, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* Scrollable content area */
.overlay-body {
  flex: 1;
  overflow-y: auto;
  padding: 24px 32px 32px 32px;
  scrollbar-width: thin;
  scrollbar-color: rgba(124, 92, 255, 0.3) transparent;
}

.overlay-body::-webkit-scrollbar {
  width: 6px;
}

.overlay-body::-webkit-scrollbar-track {
  background: transparent;
}

.overlay-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, rgba(124, 92, 255, 0.3), rgba(147, 51, 234, 0.3));
  border-radius: 3px;
}

.overlay-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, rgba(124, 92, 255, 0.5), rgba(147, 51, 234, 0.5));
}

.sorting-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.6));
  border-radius: 16px;
  box-shadow:
    0 4px 12px rgba(124, 92, 255, 0.08),
    0 0 0 1px rgba(124, 92, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
}

.sort-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-container label {
  font-weight: 500;
  color: #333;
}

.sort-select {
  padding: 8px 12px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-select:hover, .sort-select:focus {
  border-color: #7c5cff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(124, 92, 255, 0.2);
}

.sort-order-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background-color: white;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sort-order-button:hover {
  background-color: #f5f5f5;
  border-color: #7c5cff;
  color: #7c5cff;
}

.results-count {
  font-size: 14px;
  color: #666;
}

.recommendation-grid {
  display: grid;
  /* Enhanced grid for larger overlay - more products visible */
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
  /* Ensure grid utilizes full width */
  width: 100%;
}

.recommendation-grid .product-card {
  transform: scale(1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  background-color: white;
  animation: cardAppear 0.5s ease-out forwards;
  animation-delay: calc(var(--card-index, 0) * 0.1s);
  opacity: 0;
}

.recommendation-grid .product-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.no-recommendations {
  text-align: center;
  padding: 60px 0;
  color: #666;
  font-size: 18px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes floatIn {
  0% {
    transform: translateY(20px) scale(0.98);
    opacity: 0;
  }
  100% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles - Enhanced for multi-window experience */
@media (max-width: 1400px) {
  .overlay-content {
    width: calc(100% - 420px); /* Maintain chat + cart space */
    max-width: 1200px;
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 20px;
  }
}

@media (max-width: 1200px) {
  .overlay-content {
    width: calc(100% - 400px);
    max-width: 1000px;
    margin-left: 15px;
    margin-right: 15px;
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 18px;
  }
}

@media (max-width: 992px) {
  .product-recommendation-overlay {
    justify-content: flex-start;
    align-items: flex-start;
    padding: 15px;
  }

  .overlay-content {
    width: calc(100% - 380px); /* Still maintain chat accessibility */
    height: calc(100vh - 30px);
    max-height: calc(100vh - 30px);
    margin-left: 10px;
    margin-right: 10px;
    margin-top: 0;
  }

  .overlay-header h2 {
    font-size: 24px;
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .product-recommendation-overlay {
    padding: 10px;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .overlay-content {
    width: calc(100% - 380px); /* Maintain chat + cart space on tablets */
    margin-left: 5px;
    margin-right: 5px;
    margin-top: 0;
    height: calc(100vh - 20px);
    max-height: calc(100vh - 20px);
  }

  .overlay-header {
    padding: 24px 24px 20px 24px;
  }

  .overlay-header h2 {
    font-size: 20px;
  }

  .overlay-body {
    padding: 20px 24px 24px 24px;
  }

  .sorting-controls {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
    margin-bottom: 20px;
  }

  .sort-container {
    width: 100%;
  }

  .sort-select {
    flex-grow: 1;
  }

  .results-count {
    width: 100%;
    text-align: left;
    margin-top: 5px;
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 14px;
  }
}

/* Enhanced mobile experience - maintain multi-window functionality */
@media (max-width: 600px) {
  .product-recommendation-overlay {
    justify-content: flex-start;
    align-items: flex-start;
    padding: 8px;
    padding-top: 10px; /* Minimal top padding */
  }

  .overlay-content {
    width: calc(100% - 80px); /* Leave minimal space for chat bubble */
    margin-left: 5px;
    margin-right: 5px;
    height: calc(100vh - 16px);
    max-height: calc(100vh - 16px);
  }

  .overlay-header {
    padding: 20px 20px 16px 20px;
  }

  .overlay-header h2 {
    font-size: 18px;
  }

  .overlay-body {
    padding: 16px 20px 20px 20px;
  }

  .sorting-controls {
    padding: 12px;
    margin-bottom: 16px;
  }

  .recommendation-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  /* Ensure close button is easily accessible on mobile */
  .close-button {
    width: 44px;
    height: 44px;
    top: -10px;
    right: -10px;
  }
}

/* Targeted accessibility solution for essential header elements */

/* When overlay is active, add subtle background blur to non-essential elements */
body.product-overlay-active .header {
  backdrop-filter: blur(4px);
  transition: backdrop-filter 0.3s ease;
}

body.product-overlay-active .logo,
body.product-overlay-active .nav {
  opacity: 0.6;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* Essential header elements remain fully accessible and visible */
body.product-overlay-active .cart-icon-container,
body.product-overlay-active .header-actions .icon-button {
  position: relative;
  z-index: 10000; /* Above overlay */
  opacity: 1;
  pointer-events: auto;
  filter: drop-shadow(0 2px 8px rgba(124, 92, 255, 0.3));
  transition: all 0.3s ease;
}

/* Enhance visual prominence of accessible buttons */
body.product-overlay-active .cart-icon-container:hover,
body.product-overlay-active .header-actions .icon-button:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 12px rgba(124, 92, 255, 0.4));
}

/* Cart dropdown should appear above overlay with enhanced styling */
body.product-overlay-active .cart-dropdown {
  z-index: 10001; /* Above essential buttons */
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(124, 92, 255, 0.2);
}

/* Large screen optimizations */
@media (min-width: 1600px) {
  .overlay-content {
    max-width: 1600px; /* Even larger on very wide screens */
    width: calc(100% - 460px); /* More space for chat + cart */
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 28px;
  }
}

/* Ultra-wide screen support */
@media (min-width: 2000px) {
  .overlay-content {
    max-width: 1800px;
    width: calc(100% - 500px);
  }

  .recommendation-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 32px;
  }
}
