import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* University Info */}
          <div className="footer-section">
            <div className="footer-logo">
              <img src="/სსუ.jpg" alt="SSU Logo" className="footer-logo-image" />
              <div>
                <h3>სოხუმის სახელმწიფო უნივერსიტეტი</h3>
                <p>Sokhumi State University</p>
              </div>
            </div>
            <p className="footer-description">
              ჩვენი უნივერსიტეტი არის წამყვანი საგანმანათლებლო დაწესებულება, 
              რომელიც უზრუნველყოფს მაღალი ხარისხის განათლებას და კვლევას.
            </p>
            <p className="footer-description">
              Our university is a leading educational institution providing 
              high-quality education and research opportunities.
            </p>
          </div>

          {/* Quick Links */}
          <div className="footer-section">
            <h4>სწრაფი ბმულები / Quick Links</h4>
            <ul className="footer-links">
              <li><Link to="/about">ჩვენს შესახებ / About</Link></li>
              <li><Link to="/academics">აკადემიური / Academics</Link></li>
              <li><Link to="/admissions">მიღება / Admissions</Link></li>
              <li><Link to="/student-life">სტუდენტური ცხოვრება / Student Life</Link></li>
              <li><Link to="/research">კვლევა / Research</Link></li>
              <li><Link to="/library">ბიბლიოთეკა / Library</Link></li>
            </ul>
          </div>

          {/* Academic Programs */}
          <div className="footer-section">
            <h4>აკადემიური პროგრამები / Academic Programs</h4>
            <ul className="footer-links">
              <li><a href="#bachelor">ბაკალავრიატი / Bachelor's</a></li>
              <li><a href="#master">მაგისტრატურა / Master's</a></li>
              <li><a href="#doctoral">დოქტორანტურა / Doctoral</a></li>
              <li><a href="#continuing">უწყვეტი განათლება / Continuing Education</a></li>
              <li><a href="#international">საერთაშორისო პროგრამები / International Programs</a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="footer-section">
            <h4>კონტაქტი / Contact</h4>
            <div className="contact-info">
              <div className="contact-item">
                <MapPin size={18} />
                <div>
                  <p>თბილისი, საქართველო</p>
                  <p>Tbilisi, Georgia</p>
                </div>
              </div>
              <div className="contact-item">
                <Phone size={18} />
                <div>
                  <p>+995 32 2 XX XX XX</p>
                  <p>+995 32 2 XX XX XX</p>
                </div>
              </div>
              <div className="contact-item">
                <Mail size={18} />
                <div>
                  <p><EMAIL></p>
                  <p><EMAIL></p>
                </div>
              </div>
            </div>

            {/* Social Media */}
            <div className="social-media">
              <h5>გამოგვყევით / Follow Us</h5>
              <div className="social-links">
                <a href="#" aria-label="Facebook">
                  <Facebook size={20} />
                </a>
                <a href="#" aria-label="Twitter">
                  <Twitter size={20} />
                </a>
                <a href="#" aria-label="Instagram">
                  <Instagram size={20} />
                </a>
                <a href="#" aria-label="YouTube">
                  <Youtube size={20} />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Footer Bottom */}
        <div className="footer-bottom">
          <div className="footer-bottom-content">
            <p>&copy; 2025 სოხუმის სახელმწიფო უნივერსიტეტი / Sokhumi State University. ყველა უფლება დაცულია / All rights reserved.</p>
            <div className="footer-bottom-links">
              <a href="#privacy">კონფიდენციალურობა / Privacy Policy</a>
              <a href="#terms">წესები / Terms of Service</a>
              <a href="#accessibility">ხელმისაწვდომობა / Accessibility</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
