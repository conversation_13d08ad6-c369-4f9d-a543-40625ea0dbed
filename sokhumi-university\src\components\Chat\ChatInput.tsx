import { useState, useRef, useEffect } from 'react';
import type { FC } from 'react';
import { useChat } from '../../context/ChatContext';
import './ChatInput.css';

const ChatInput: FC = () => {
  const { sendMessage, sendImage, config, state, setDraftMessage } = useChat();
  const [message, setMessage] = useState(state.draftMessage);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Focus input when component mounts and adjust height if there's a draft message
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();

      // If there's a draft message, adjust the textarea height
      if (state.draftMessage) {
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.style.height = 'auto';
            inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 120)}px`;
          }
        }, 0);
      }
    }
  }, [state.draftMessage]);

  // Reset height when message is empty
  useEffect(() => {
    if (message === '' && inputRef.current) {
      inputRef.current.style.height = 'auto';
    }
  }, [message]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      sendMessage(message);
      setMessage('');

      // Clear draft message in context
      setDraftMessage('');

      // Reset textarea height after sending
      if (inputRef.current) {
        inputRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newMessage = e.target.value;
    setMessage(newMessage);

    // Save draft message to context
    setDraftMessage(newMessage);

    // Auto-resize textarea
    const textarea = e.target;
    textarea.style.height = 'auto';
    textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;
  };

  // Handle file selection
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];

    // Check if file is an image
    if (!file.type.startsWith('image/')) {
      alert('მხოლოდ სურათების ფაილები არის მხარდაჭერილი.');
      return;
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('ფაილის ზომა არ უნდა აღემატებოდეს 5MB-ს.');
      return;
    }

    // Send the image
    sendImage(file);

    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Trigger file input click
  const handleAttachmentClick = () => {
    fileInputRef.current?.click();
  };

  // Handle paste event (for pasting images)
  const handlePaste = (e: React.ClipboardEvent) => {
    const items = e.clipboardData?.items;
    if (!items) return;

    // Find the first image item in the clipboard
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        // Get the image as a file
        const file = items[i].getAsFile();
        if (!file) continue;

        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
          alert('ფაილის ზომა არ უნდა აღემატებოდეს 5MB-ს.');
          return;
        }

        // Send the image
        sendImage(file);

        // Prevent the default paste behavior for images
        e.preventDefault();
        return;
      }
    }
  };

  return (
    <form className="chat-input-container" onSubmit={handleSubmit} title="რჩევა: შეგიძლიათ სურათები პირდაპირ ჩასვათ">
      <textarea
        ref={inputRef}
        className="chat-input chat-focus-visible"
        placeholder="დაწერეთ შეტყობინება..."
        value={message}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        onPaste={handlePaste}
        rows={1}
        style={{
          borderColor: (config.theme?.primaryColor || '#1e40af') + '40' // 40 is for 25% opacity in hex
        }}
      />

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        style={{ display: 'none' }}
      />

      {config.enableAttachments && (
        <button
          type="button"
          className="chat-input-attachment chat-focus-visible"
          aria-label="Attach file"
          onClick={handleAttachmentClick}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21.44 11.05L12.25 20.24C11.1242 21.3658 9.59723 21.9983 8.005 21.9983C6.41277 21.9983 4.88584 21.3658 3.76 20.24C2.63416 19.1142 2.00166 17.5872 2.00166 15.995C2.00166 14.4028 2.63416 12.8758 3.76 11.75L12.33 3.18C13.0806 2.42975 14.0991 2.00129 15.16 2.00129C16.2209 2.00129 17.2394 2.42975 17.99 3.18C18.7403 3.93063 19.1687 4.94913 19.1687 6.01C19.1687 7.07088 18.7403 8.08938 17.99 8.84L9.41 17.41C9.03472 17.7853 8.52573 17.9961 7.995 17.9961C7.46427 17.9961 6.95528 17.7853 6.58 17.41C6.20472 17.0347 5.99389 16.5257 5.99389 15.995C5.99389 15.4643 6.20472 14.9553 6.58 14.58L15.07 6.1" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      )}

      <button
        type="submit"
        className="chat-input-send chat-focus-visible"
        disabled={!message.trim()}
        aria-label="Send message"
        style={{
          backgroundColor: config.theme?.primaryColor || '#1e40af',
          opacity: message.trim() ? 1 : 0.6
        }}
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M22 2L11 13" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </button>
    </form>
  );
};

export default ChatInput;
