.chat-input-container {
  display: flex;
  align-items: flex-end;
  padding: 12px 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  background-color: var(--chat-background-color);
  border-bottom-left-radius: var(--chat-border-radius);
  border-bottom-right-radius: var(--chat-border-radius);
  position: relative;
}

.chat-input {
  flex: 1;
  border: 1px solid var(--chat-input-border);
  border-radius: 24px;
  padding: 12px 16px;
  font-family: var(--chat-font-family);
  font-size: 14px;
  resize: none;
  max-height: 120px;
  background-color: var(--chat-input-bg);
  color: var(--chat-text-color);
  transition: border-color 0.2s;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
  position: relative;
}

/* Tooltip for paste hint */
.chat-input-container::after {
  content: "Tip: You can paste images directly";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s, top 0.3s;
  pointer-events: none;
  white-space: nowrap;
  z-index: 1000;
}

.chat-input:focus + .chat-input-attachment,
.chat-input:focus + input + .chat-input-attachment {
  opacity: 1;
}

.chat-input:focus ~ button {
  opacity: 1;
}

.chat-input-container:hover::after {
  opacity: 0.8;
  top: -25px;
}

/* Custom scrollbar for WebKit browsers */
.chat-input::-webkit-scrollbar {
  width: 4px;
}

.chat-input::-webkit-scrollbar-track {
  background: transparent;
}

.chat-input::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.chat-input::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.chat-input:focus {
  outline: none;
  border-color: var(--chat-primary-color);
}

.chat-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.chat-input-attachment,
.chat-input-send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  margin-left: 8px;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.2s;
}

.chat-input-attachment {
  background-color: transparent;
  color: var(--chat-text-color);
  opacity: 0.6;
}

.chat-input-attachment:hover {
  background-color: rgba(255, 255, 255, 0.1);
  opacity: 0.8;
}

.chat-input-send {
  background-color: var(--chat-primary-color);
  color: white;
}

.chat-input-send:hover:not(:disabled) {
  transform: scale(1.05);
}

.chat-input-send:disabled {
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .chat-input-container {
    padding: 8px 12px;
  }

  .chat-input {
    padding: 10px 14px;
    font-size: 14px;
  }

  .chat-input-attachment,
  .chat-input-send {
    width: 36px;
    height: 36px;
  }
}
