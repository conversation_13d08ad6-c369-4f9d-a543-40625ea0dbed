/**
 * Georgian Translation Service
 * High-quality local translation for Georgian ↔ English
 */

// Georgian Unicode ranges
const GEORGIAN_RANGES = {
  // Georgian (Mkhedruli) - main modern script
  mkhedruli: /[\u10A0-\u10FF]/,
  // Georgian (Asomtavruli) - ancient script
  asomtavruli: /[\u10A0-\u10C5]/,
  // Georgian (Nuskhuri) - ecclesiastical script  
  nuskhuri: /[\u2D00-\u2D25]/,
  // Georgian Extended
  extended: /[\u1C90-\u1CBF]/
};

// Common e-commerce terms dictionary
const GEORGIAN_DICTIONARY: Record<string, string> = {
  // Shopping terms
  'კალათა': 'cart',
  'კალათაში დამატება': 'add to cart',
  'კალათიდან ამოღება': 'remove from cart',
  'შეძენა': 'buy',
  'ყიდვა': 'purchase',
  'ფასი': 'price',
  'ღირებულება': 'cost',
  'ფული': 'money',
  'ანგარიშსწორება': 'checkout',
  'გადახდა': 'payment',
  'მიწოდება': 'delivery',
  'ტრანსპორტირება': 'shipping',
  'უფასო მიწოდება': 'free shipping',
  'შეკვეთა': 'order',
  'პროდუქტი': 'product',
  'პროდუქცია': 'products',
  'ნივთი': 'item',
  'ნივთები': 'items',
  'ტოვარი': 'goods',
  'კატეგორია': 'category',
  'ბრენდი': 'brand',
  'მარკა': 'brand',
  'ხარისხი': 'quality',
  'რეიტინგი': 'rating',
  'შეფასება': 'rating',
  'მიმოხილვა': 'review',
  'კომენტარი': 'comment',
  'ძიება': 'search',
  'ძებნა': 'search',
  'მოძებნა': 'find',
  'ფილტრი': 'filter',
  'დალაგება': 'sort',
  'სორტირება': 'sorting',
  
  // Product categories
  'ტელეფონი': 'phone',
  'სმარტფონი': 'smartphone',
  'ლეპტოპი': 'laptop',
  'კომპიუტერი': 'computer',
  'ყურსასმენი': 'headphones',
  'ყურსაცვამი': 'earphones',
  'საათი': 'watch',
  'ჭკვიანი საათი': 'smart watch',
  'ტაბლეტი': 'tablet',
  'კამერა': 'camera',
  'ფოტოაპარატი': 'camera',
  'ტელევიზორი': 'tv',
  'მონიტორი': 'monitor',
  'კლავიატურა': 'keyboard',
  'მაუსი': 'mouse',
  'დინამიკი': 'speaker',
  'ბატარეა': 'battery',
  'დამტენი': 'charger',
  'კაბელი': 'cable',
  'ადაპტერი': 'adapter',
  
  // Common phrases
  'გამარჯობა': 'hello',
  'გაუმარჯოს': 'hello',
  'მადლობა': 'thank you',
  'გმადლობთ': 'thank you',
  'კი': 'yes',
  'არა': 'no',
  'დიახ': 'yes',
  'კარგი': 'good',
  'ცუდი': 'bad',
  'ძვირი': 'expensive',
  'იაფი': 'cheap',
  'ახალი': 'new',
  'ძველი': 'old',
  'დიდი': 'big',
  'პატარა': 'small',
  'სწრაფი': 'fast',
  'ნელი': 'slow',
  'ლამაზი': 'beautiful',
  'მშვენიერი': 'beautiful',
  'საუკეთესო': 'best',
  'ყველაზე კარგი': 'best',
  'რეკომენდაცია': 'recommendation',
  'რჩევა': 'advice',
  'დახმარება': 'help',
  'მხარდაჭერა': 'support',
  'ინფორმაცია': 'information',
  'დეტალები': 'details',
  'სპეციფიკაცია': 'specification',
  'მახასიათებლები': 'features',
  'თვისებები': 'properties',
  
  // Questions
  'რა': 'what',
  'როგორ': 'how',
  'სად': 'where',
  'როდის': 'when',
  'რატომ': 'why',
  'ვინ': 'who',
  'რამდენი': 'how much',
  'რამდენი ღირს': 'how much does it cost',
  'რა ფასი აქვს': 'what is the price',
  'შეგიძლია': 'can you',
  'შეძლებ': 'can you',
  'გთხოვ': 'please',
  'გეთაყვა': 'please',
  'მინდა': 'I want',
  'მსურს': 'I want',
  'მჭირდება': 'I need',
  'ვეძებ': 'I am looking for',
  'ვეძიებ': 'I am searching',
  'მაჩვენე': 'show me',
  'მითხარი': 'tell me',
  'ახსენი': 'explain',
  'გაიგე': 'find out',
  'შეამოწმე': 'check',
  'დაამატე': 'add',
  'ამოიღე': 'remove',
  'წაშალე': 'delete',
  'შეცვალე': 'change',
  'განაახლე': 'update'
};

// Reverse dictionary for English to Georgian
const ENGLISH_DICTIONARY: Record<string, string> = {};
Object.entries(GEORGIAN_DICTIONARY).forEach(([georgian, english]) => {
  ENGLISH_DICTIONARY[english] = georgian;
});

export interface TranslationResult {
  translatedText: string;
  originalText: string;
  sourceLanguage: 'ka' | 'en';
  targetLanguage: 'ka' | 'en';
  confidence: number;
  method: 'dictionary' | 'pattern' | 'fallback';
}

export class GeorgianTranslationService {
  
  /**
   * Detect if text contains Georgian characters
   */
  static detectLanguage(text: string): 'ka' | 'en' {
    if (!text || text.trim().length === 0) return 'en';
    
    // Check for Georgian Unicode characters
    const hasGeorgian = GEORGIAN_RANGES.mkhedruli.test(text) ||
                       GEORGIAN_RANGES.asomtavruli.test(text) ||
                       GEORGIAN_RANGES.nuskhuri.test(text) ||
                       GEORGIAN_RANGES.extended.test(text);
    
    return hasGeorgian ? 'ka' : 'en';
  }

  /**
   * Get language confidence score
   */
  static getLanguageConfidence(text: string): number {
    const totalChars = text.length;
    if (totalChars === 0) return 0;
    
    const georgianChars = (text.match(/[\u10A0-\u10FF\u2D00-\u2D25\u1C90-\u1CBF]/g) || []).length;
    return georgianChars / totalChars;
  }

  /**
   * Translate Georgian to English
   */
  static async translateToEnglish(georgianText: string): Promise<TranslationResult> {
    const originalText = georgianText.trim();
    
    if (!originalText) {
      return {
        translatedText: '',
        originalText,
        sourceLanguage: 'ka',
        targetLanguage: 'en',
        confidence: 1,
        method: 'dictionary'
      };
    }

    // Try dictionary-based translation first
    const dictionaryResult = this.translateWithDictionary(originalText, 'ka', 'en');
    if (dictionaryResult.confidence > 0.7) {
      return dictionaryResult;
    }

    // Try pattern-based translation
    const patternResult = this.translateWithPatterns(originalText, 'ka', 'en');
    if (patternResult.confidence > 0.5) {
      return patternResult;
    }

    // Fallback to basic word replacement
    return this.translateWithFallback(originalText, 'ka', 'en');
  }

  /**
   * Translate English to Georgian
   */
  static async translateToGeorgian(englishText: string): Promise<TranslationResult> {
    const originalText = englishText.trim();
    
    if (!originalText) {
      return {
        translatedText: '',
        originalText,
        sourceLanguage: 'en',
        targetLanguage: 'ka',
        confidence: 1,
        method: 'dictionary'
      };
    }

    // Try dictionary-based translation first
    const dictionaryResult = this.translateWithDictionary(originalText, 'en', 'ka');
    if (dictionaryResult.confidence > 0.7) {
      return dictionaryResult;
    }

    // Try pattern-based translation
    const patternResult = this.translateWithPatterns(originalText, 'en', 'ka');
    if (patternResult.confidence > 0.5) {
      return patternResult;
    }

    // Fallback to basic word replacement
    return this.translateWithFallback(originalText, 'en', 'ka');
  }

  /**
   * Dictionary-based translation
   */
  private static translateWithDictionary(
    text: string, 
    sourceLanguage: 'ka' | 'en', 
    targetLanguage: 'ka' | 'en'
  ): TranslationResult {
    const dictionary = sourceLanguage === 'ka' ? GEORGIAN_DICTIONARY : ENGLISH_DICTIONARY;
    const lowerText = text.toLowerCase();
    
    // Check for exact matches
    if (dictionary[lowerText]) {
      return {
        translatedText: dictionary[lowerText],
        originalText: text,
        sourceLanguage,
        targetLanguage,
        confidence: 1,
        method: 'dictionary'
      };
    }

    // Check for partial matches
    const words = text.split(/\s+/);
    const translatedWords: string[] = [];
    let matchCount = 0;

    for (const word of words) {
      const cleanWord = word.toLowerCase().replace(/[.,!?;:]/g, '');
      if (dictionary[cleanWord]) {
        translatedWords.push(dictionary[cleanWord]);
        matchCount++;
      } else {
        translatedWords.push(word);
      }
    }

    const confidence = words.length > 0 ? matchCount / words.length : 0;

    return {
      translatedText: translatedWords.join(' '),
      originalText: text,
      sourceLanguage,
      targetLanguage,
      confidence,
      method: 'dictionary'
    };
  }

  /**
   * Pattern-based translation for common phrases
   */
  private static translateWithPatterns(
    text: string,
    sourceLanguage: 'ka' | 'en',
    targetLanguage: 'ka' | 'en'
  ): TranslationResult {
    const patterns = sourceLanguage === 'ka' ? this.getGeorgianPatterns() : this.getEnglishPatterns();
    
    for (const pattern of patterns) {
      const match = text.match(pattern.regex);
      if (match) {
        const translatedText = pattern.translate(match);
        return {
          translatedText,
          originalText: text,
          sourceLanguage,
          targetLanguage,
          confidence: 0.8,
          method: 'pattern'
        };
      }
    }

    return {
      translatedText: text,
      originalText: text,
      sourceLanguage,
      targetLanguage,
      confidence: 0,
      method: 'pattern'
    };
  }

  /**
   * Fallback translation method
   */
  private static translateWithFallback(
    text: string,
    sourceLanguage: 'ka' | 'en',
    targetLanguage: 'ka' | 'en'
  ): TranslationResult {
    // For fallback, we'll do basic word-by-word replacement
    const dictionary = sourceLanguage === 'ka' ? GEORGIAN_DICTIONARY : ENGLISH_DICTIONARY;
    const words = text.split(/\s+/);
    const translatedWords: string[] = [];

    for (const word of words) {
      const cleanWord = word.toLowerCase().replace(/[.,!?;:]/g, '');
      const punctuation = word.match(/[.,!?;:]/g)?.join('') || '';
      
      if (dictionary[cleanWord]) {
        translatedWords.push(dictionary[cleanWord] + punctuation);
      } else {
        translatedWords.push(word);
      }
    }

    return {
      translatedText: translatedWords.join(' '),
      originalText: text,
      sourceLanguage,
      targetLanguage,
      confidence: 0.3,
      method: 'fallback'
    };
  }

  /**
   * Georgian language patterns
   */
  private static getGeorgianPatterns() {
    return [
      {
        regex: /მაჩვენე\s+(.+)/i,
        translate: (match: RegExpMatchArray) => `show me ${match[1]}`
      },
      {
        regex: /ვეძებ\s+(.+)/i,
        translate: (match: RegExpMatchArray) => `I am looking for ${match[1]}`
      },
      {
        regex: /რამდენი\s+ღირს\s+(.+)/i,
        translate: (match: RegExpMatchArray) => `how much does ${match[1]} cost`
      },
      {
        regex: /დაამატე\s+(.+)\s+კალათაში/i,
        translate: (match: RegExpMatchArray) => `add ${match[1]} to cart`
      },
      {
        regex: /ამოიღე\s+(.+)\s+კალათიდან/i,
        translate: (match: RegExpMatchArray) => `remove ${match[1]} from cart`
      }
    ];
  }

  /**
   * English language patterns
   */
  private static getEnglishPatterns() {
    return [
      {
        regex: /show me (.+)/i,
        translate: (match: RegExpMatchArray) => `მაჩვენე ${match[1]}`
      },
      {
        regex: /I am looking for (.+)/i,
        translate: (match: RegExpMatchArray) => `ვეძებ ${match[1]}`
      },
      {
        regex: /how much does (.+) cost/i,
        translate: (match: RegExpMatchArray) => `რამდენი ღირს ${match[1]}`
      },
      {
        regex: /add (.+) to cart/i,
        translate: (match: RegExpMatchArray) => `დაამატე ${match[1]} კალათაში`
      },
      {
        regex: /remove (.+) from cart/i,
        translate: (match: RegExpMatchArray) => `ამოიღე ${match[1]} კალათიდან`
      }
    ];
  }

  /**
   * Preserve product IDs and technical terms
   */
  static preserveProductTerms(text: string): string {
    // Preserve product IDs (p001, p121, etc.)
    text = text.replace(/\bp\d+\b/g, (match) => `__PRODUCT_ID_${match}__`);
    
    // Preserve technical terms
    const technicalTerms = ['iPhone', 'MacBook', 'Samsung', 'Sony', 'LG', 'Dell', 'HP', 'Lenovo'];
    technicalTerms.forEach(term => {
      const regex = new RegExp(`\\b${term}\\b`, 'gi');
      text = text.replace(regex, `__TECH_TERM_${term}__`);
    });
    
    return text;
  }

  /**
   * Restore preserved terms
   */
  static restoreProductTerms(text: string): string {
    // Restore product IDs
    text = text.replace(/__PRODUCT_ID_([^_]+)__/g, '$1');
    
    // Restore technical terms
    text = text.replace(/__TECH_TERM_([^_]+)__/g, '$1');
    
    return text;
  }
}
