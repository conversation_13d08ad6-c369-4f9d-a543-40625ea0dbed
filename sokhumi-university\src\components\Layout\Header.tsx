import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAcademicsOpen, setIsAcademicsOpen] = useState(false);
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <Link to="/" className="logo" onClick={closeMenu}>
            <img src="/სსუ.jpg" alt="სსუ ლოგო" className="logo-image" />
            <div className="logo-text">
              <h1>სოხუმის სახელმწიფო უნივერსიტეტი</h1>
              <p>განათლება • კვლევა • ინოვაცია</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="nav-desktop">
            <ul className="nav-list">
              <li>
                <Link
                  to="/"
                  className={`nav-link ${isActive('/') ? 'active' : ''}`}
                >
                  მთავარი
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className={`nav-link ${isActive('/about') ? 'active' : ''}`}
                >
                  ჩვენს შესახებ
                </Link>
              </li>
              <li className="nav-dropdown">
                <button
                  className="nav-link dropdown-toggle"
                  onMouseEnter={() => setIsAcademicsOpen(true)}
                  onMouseLeave={() => setIsAcademicsOpen(false)}
                >
                  აკადემიური
                  <ChevronDown size={16} />
                </button>
                {isAcademicsOpen && (
                  <div
                    className="dropdown-menu"
                    onMouseEnter={() => setIsAcademicsOpen(true)}
                    onMouseLeave={() => setIsAcademicsOpen(false)}
                  >
                    <Link to="/academics" className="dropdown-link">
                      ფაკულტეტები
                    </Link>
                    <Link to="/faculty" className="dropdown-link">
                      პროფესორები
                    </Link>
                    <Link to="/library" className="dropdown-link">
                      ბიბლიოთეკა
                    </Link>
                  </div>
                )}
              </li>
              <li>
                <Link
                  to="/admissions"
                  className={`nav-link ${isActive('/admissions') ? 'active' : ''}`}
                >
                  მიღება
                </Link>
              </li>
              <li>
                <Link
                  to="/student-life"
                  className={`nav-link ${isActive('/student-life') ? 'active' : ''}`}
                >
                  სტუდენტური ცხოვრება
                </Link>
              </li>
              <li>
                <Link
                  to="/research"
                  className={`nav-link ${isActive('/research') ? 'active' : ''}`}
                >
                  კვლევა
                </Link>
              </li>
              <li>
                <Link
                  to="/news"
                  className={`nav-link ${isActive('/news') ? 'active' : ''}`}
                >
                  სიახლეები
                </Link>
              </li>
              <li>
                <Link
                  to="/contact"
                  className={`nav-link ${isActive('/contact') ? 'active' : ''}`}
                >
                  კონტაქტი
                </Link>
              </li>
            </ul>
          </nav>

          {/* Mobile Menu Button */}
          <button className="mobile-menu-btn" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="nav-mobile">
            <ul className="nav-list-mobile">
              <li>
                <Link to="/" className="nav-link-mobile" onClick={closeMenu}>
                  მთავარი
                </Link>
              </li>
              <li>
                <Link to="/about" className="nav-link-mobile" onClick={closeMenu}>
                  ჩვენს შესახებ
                </Link>
              </li>
              <li>
                <Link to="/academics" className="nav-link-mobile" onClick={closeMenu}>
                  აკადემიური
                </Link>
              </li>
              <li>
                <Link to="/faculty" className="nav-link-mobile" onClick={closeMenu}>
                  პროფესორები
                </Link>
              </li>
              <li>
                <Link to="/admissions" className="nav-link-mobile" onClick={closeMenu}>
                  მიღება
                </Link>
              </li>
              <li>
                <Link to="/student-life" className="nav-link-mobile" onClick={closeMenu}>
                  სტუდენტური ცხოვრება
                </Link>
              </li>
              <li>
                <Link to="/research" className="nav-link-mobile" onClick={closeMenu}>
                  კვლევა
                </Link>
              </li>
              <li>
                <Link to="/library" className="nav-link-mobile" onClick={closeMenu}>
                  ბიბლიოთეკა
                </Link>
              </li>
              <li>
                <Link to="/news" className="nav-link-mobile" onClick={closeMenu}>
                  სიახლეები
                </Link>
              </li>
              <li>
                <Link to="/contact" className="nav-link-mobile" onClick={closeMenu}>
                  კონტაქტი
                </Link>
              </li>
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
