import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, ChevronDown } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAcademicsOpen, setIsAcademicsOpen] = useState(false);
  const location = useLocation();

  const isActive = (path: string) => location.pathname === path;

  const toggleMenu = () => setIsMenuOpen(!isMenuOpen);
  const closeMenu = () => setIsMenuOpen(false);

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <Link to="/" className="logo" onClick={closeMenu}>
            <img src="/სსუ.jpg" alt="SSU Logo" className="logo-image" />
            <div className="logo-text">
              <h1>სოხუმის სახელმწიფო უნივერსიტეტი</h1>
              <p>Sokhumi State University</p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="nav-desktop">
            <ul className="nav-list">
              <li>
                <Link 
                  to="/" 
                  className={`nav-link ${isActive('/') ? 'active' : ''}`}
                >
                  მთავარი / Home
                </Link>
              </li>
              <li>
                <Link 
                  to="/about" 
                  className={`nav-link ${isActive('/about') ? 'active' : ''}`}
                >
                  ჩვენს შესახებ / About
                </Link>
              </li>
              <li className="nav-dropdown">
                <button 
                  className="nav-link dropdown-toggle"
                  onMouseEnter={() => setIsAcademicsOpen(true)}
                  onMouseLeave={() => setIsAcademicsOpen(false)}
                >
                  აკადემიური / Academics
                  <ChevronDown size={16} />
                </button>
                {isAcademicsOpen && (
                  <div 
                    className="dropdown-menu"
                    onMouseEnter={() => setIsAcademicsOpen(true)}
                    onMouseLeave={() => setIsAcademicsOpen(false)}
                  >
                    <Link to="/academics" className="dropdown-link">
                      ფაკულტეტები / Faculties
                    </Link>
                    <Link to="/faculty" className="dropdown-link">
                      პროფესორები / Faculty
                    </Link>
                    <Link to="/library" className="dropdown-link">
                      ბიბლიოთეკა / Library
                    </Link>
                  </div>
                )}
              </li>
              <li>
                <Link 
                  to="/admissions" 
                  className={`nav-link ${isActive('/admissions') ? 'active' : ''}`}
                >
                  მიღება / Admissions
                </Link>
              </li>
              <li>
                <Link 
                  to="/student-life" 
                  className={`nav-link ${isActive('/student-life') ? 'active' : ''}`}
                >
                  სტუდენტური ცხოვრება / Student Life
                </Link>
              </li>
              <li>
                <Link 
                  to="/research" 
                  className={`nav-link ${isActive('/research') ? 'active' : ''}`}
                >
                  კვლევა / Research
                </Link>
              </li>
              <li>
                <Link 
                  to="/news" 
                  className={`nav-link ${isActive('/news') ? 'active' : ''}`}
                >
                  სიახლეები / News
                </Link>
              </li>
              <li>
                <Link 
                  to="/contact" 
                  className={`nav-link ${isActive('/contact') ? 'active' : ''}`}
                >
                  კონტაქტი / Contact
                </Link>
              </li>
            </ul>
          </nav>

          {/* Mobile Menu Button */}
          <button className="mobile-menu-btn" onClick={toggleMenu}>
            {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="nav-mobile">
            <ul className="nav-list-mobile">
              <li>
                <Link to="/" className="nav-link-mobile" onClick={closeMenu}>
                  მთავარი / Home
                </Link>
              </li>
              <li>
                <Link to="/about" className="nav-link-mobile" onClick={closeMenu}>
                  ჩვენს შესახებ / About
                </Link>
              </li>
              <li>
                <Link to="/academics" className="nav-link-mobile" onClick={closeMenu}>
                  აკადემიური / Academics
                </Link>
              </li>
              <li>
                <Link to="/faculty" className="nav-link-mobile" onClick={closeMenu}>
                  პროფესორები / Faculty
                </Link>
              </li>
              <li>
                <Link to="/admissions" className="nav-link-mobile" onClick={closeMenu}>
                  მიღება / Admissions
                </Link>
              </li>
              <li>
                <Link to="/student-life" className="nav-link-mobile" onClick={closeMenu}>
                  სტუდენტური ცხოვრება / Student Life
                </Link>
              </li>
              <li>
                <Link to="/research" className="nav-link-mobile" onClick={closeMenu}>
                  კვლევა / Research
                </Link>
              </li>
              <li>
                <Link to="/library" className="nav-link-mobile" onClick={closeMenu}>
                  ბიბლიოთეკა / Library
                </Link>
              </li>
              <li>
                <Link to="/news" className="nav-link-mobile" onClick={closeMenu}>
                  სიახლეები / News
                </Link>
              </li>
              <li>
                <Link to="/contact" className="nav-link-mobile" onClick={closeMenu}>
                  კონტაქტი / Contact
                </Link>
              </li>
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};

export default Header;
