import knowledgeBase from '../data/university-knowledge-base.json';
import cacheService from './cacheService';

// Types for knowledge base structure
export interface SearchResult {
  category: string;
  subcategory?: string;
  data: any;
  relevanceScore: number;
  source: string;
  summary: string;
}

export interface SearchOptions {
  categories?: string[];
  maxResults?: number;
  minRelevanceScore?: number;
  includeGeorgian?: boolean;
}

// Knowledge base categories mapping
const CATEGORY_KEYWORDS = {
  academicPrograms: [
    'program', 'programs', 'course', 'courses', 'degree', 'degrees', 'major', 'majors',
    'study', 'studies', 'curriculum', 'academic', 'bachelor', 'master', 'undergraduate', 'graduate',
    'computer science', 'business', 'psychology', 'education', 'cs', 'it', 'programming',
    'პროგრამა', 'პროგრამები', 'კურსი', 'კურსები', 'ხარისხი', 'სწავლა', 'აკადემიური',
    'ბაკალავრი', 'მაგისტრი', 'კომპიუტერული', 'ბიზნესი', 'ფსიქოლოგია', 'განათლება'
  ],
  faculty: [
    'faculty', 'professor', 'professors', 'teacher', 'teachers', 'staff', 'instructor',
    'department', 'departments', 'head', 'contact', 'email', 'phone', 'office',
    'ფაკულტეტი', 'პროფესორი', 'პროფესორები', 'მასწავლებელი', 'თანამშრომელი',
    'დეპარტამენტი', 'ხელმძღვანელი', 'კონტაქტი', 'ელფოსტა', 'ტელეფონი', 'ოფისი'
  ],
  admissions: [
    'admission', 'admissions', 'apply', 'application', 'requirements', 'deadline',
    'entrance', 'exam', 'fee', 'fees', 'scholarship', 'scholarships', 'enroll',
    'ჩარიცხვა', 'განაცხადი', 'მოთხოვნები', 'ვადა', 'გამოცდა', 'ღირებულება',
    'სტიპენდია', 'რეგისტრაცია'
  ],
  research: [
    'research', 'center', 'centers', 'lab', 'laboratory', 'project', 'projects',
    'publication', 'publications', 'innovation', 'ai', 'artificial intelligence',
    'კვლევა', 'ცენტრი', 'ლაბორატორია', 'პროექტი', 'პროექტები', 'პუბლიკაცია',
    'ინოვაცია', 'ხელოვნური ინტელექტი'
  ],
  studentServices: [
    'service', 'services', 'support', 'tutoring', 'library', 'housing', 'dormitory',
    'dining', 'cafeteria', 'club', 'clubs', 'health', 'medical', 'student life',
    'სერვისი', 'მხარდაჭერა', 'რეპეტიტორობა', 'ბიბლიოთეკა', 'საცხოვრებელი',
    'კაფეტერია', 'კლუბი', 'ჯანმრთელობა', 'სამედიცინო', 'სტუდენტური ცხოვრება'
  ],
  campusFacilities: [
    'facility', 'facilities', 'building', 'buildings', 'campus', 'location',
    'parking', 'wifi', 'computer', 'lab', 'technology', 'accessibility',
    'ობიექტი', 'შენობა', 'შენობები', 'კამპუსი', 'ადგილი', 'პარკინგი',
    'ვაიფაი', 'კომპიუტერი', 'ლაბორატორია', 'ტექნოლოგია'
  ],
  events: [
    'event', 'events', 'orientation', 'graduation', 'ceremony', 'cultural',
    'research day', 'conference', 'festival', 'activity', 'activities',
    'ღონისძიება', 'ღონისძიებები', 'ორიენტაცია', 'დამთავრება', 'ცერემონია',
    'კულტურული', 'კონფერენცია', 'ფესტივალი', 'აქტივობა'
  ],
  policies: [
    'policy', 'policies', 'rule', 'rules', 'regulation', 'regulations',
    'grading', 'attendance', 'conduct', 'academic integrity', 'discipline',
    'პოლიტიკა', 'წესი', 'წესები', 'რეგულაცია', 'შეფასება', 'დასწრება',
    'ქცევა', 'აკადემიური კეთილსინდისიერება', 'დისციპლინა'
  ],
  contact: [
    'contact', 'phone', 'email', 'address', 'location', 'office', 'emergency',
    'department', 'information', 'reach', 'call', 'write',
    'კონტაქტი', 'ტელეფონი', 'ელფოსტა', 'მისამართი', 'ადგილი', 'ოფისი',
    'საგანგებო', 'დეპარტამენტი', 'ინფორმაცია'
  ]
};

// Semantic search keywords for better understanding
const SEMANTIC_KEYWORDS = {
  cost: ['cost', 'price', 'fee', 'fees', 'tuition', 'expensive', 'cheap', 'money', 'pay', 'payment', 'ღირებულება', 'ფასი', 'ფული', 'გადახდა'],
  time: ['time', 'schedule', 'hours', 'when', 'date', 'deadline', 'duration', 'დრო', 'განრიგი', 'საათები', 'როდის', 'თარიღი', 'ვადა'],
  location: ['where', 'location', 'address', 'building', 'room', 'floor', 'campus', 'სად', 'ადგილი', 'მისამართი', 'შენობა', 'ოთახი'],
  requirements: ['requirement', 'requirements', 'need', 'necessary', 'must', 'should', 'prerequisite', 'მოთხოვნა', 'საჭირო', 'უნდა'],
  how: ['how', 'process', 'procedure', 'step', 'steps', 'way', 'method', 'როგორ', 'პროცესი', 'პროცედურა', 'ნაბიჯი', 'გზა']
};

class KnowledgeBaseService {
  private knowledgeBase: any;
  private searchIndex: Map<string, any[]>;

  constructor() {
    this.knowledgeBase = knowledgeBase;
    this.searchIndex = new Map();
    this.buildSearchIndex();
  }

  /**
   * Build search index for faster retrieval
   */
  private buildSearchIndex(): void {
    console.log('🔍 Building knowledge base search index...');
    
    // Index academic programs
    this.indexCategory('academicPrograms', this.knowledgeBase.academicPrograms);
    
    // Index faculty
    this.indexCategory('faculty', this.knowledgeBase.faculty);
    
    // Index admissions
    this.indexCategory('admissions', this.knowledgeBase.admissions);
    
    // Index research
    this.indexCategory('research', this.knowledgeBase.research);
    
    // Index student services
    this.indexCategory('studentServices', this.knowledgeBase.studentServices);
    
    // Index campus facilities
    this.indexCategory('campusFacilities', this.knowledgeBase.campusFacilities);
    
    // Index events
    this.indexCategory('events', this.knowledgeBase.events);
    
    // Index policies
    this.indexCategory('policies', this.knowledgeBase.policies);
    
    // Index contact information
    this.indexCategory('contact', this.knowledgeBase.contact);

    console.log('✅ Search index built successfully');
  }

  /**
   * Index a category for search
   */
  private indexCategory(category: string, data: any): void {
    const items: any[] = [];
    this.flattenObject(data, items, category);
    this.searchIndex.set(category, items);
  }

  /**
   * Flatten nested objects for indexing
   */
  private flattenObject(obj: any, items: any[], category: string, path: string = ''): void {
    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;
      
      if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
        // If it's an object with meaningful data, add it as an item
        if (this.hasSearchableContent(value)) {
          items.push({
            key: currentPath,
            data: value,
            category,
            subcategory: key
          });
        }
        
        // Continue flattening
        this.flattenObject(value, items, category, currentPath);
      }
    }
  }

  /**
   * Check if an object has searchable content
   */
  private hasSearchableContent(obj: any): boolean {
    return obj.hasOwnProperty('name') || 
           obj.hasOwnProperty('description') || 
           obj.hasOwnProperty('title') ||
           obj.hasOwnProperty('nameGeorgian') ||
           obj.hasOwnProperty('descriptionGeorgian');
  }

  /**
   * Calculate relevance score for search results
   */
  private calculateRelevanceScore(query: string, item: any): number {
    const queryLower = query.toLowerCase();
    let score = 0;
    
    // Check different fields with different weights
    const fields = [
      { field: 'name', weight: 3 },
      { field: 'nameGeorgian', weight: 3 },
      { field: 'title', weight: 3 },
      { field: 'description', weight: 2 },
      { field: 'descriptionGeorgian', weight: 2 },
      { field: 'specialization', weight: 2 },
      { field: 'courses', weight: 1.5 },
      { field: 'services', weight: 1.5 },
      { field: 'activities', weight: 1.5 }
    ];

    for (const { field, weight } of fields) {
      if (item.data[field]) {
        const fieldValue = Array.isArray(item.data[field]) 
          ? item.data[field].join(' ').toLowerCase()
          : item.data[field].toLowerCase();
        
        // Exact match
        if (fieldValue.includes(queryLower)) {
          score += weight * 2;
        }
        
        // Word match
        const queryWords = queryLower.split(' ');
        for (const word of queryWords) {
          if (fieldValue.includes(word)) {
            score += weight * 0.5;
          }
        }
      }
    }

    // Category relevance bonus
    const categoryKeywords = CATEGORY_KEYWORDS[item.category as keyof typeof CATEGORY_KEYWORDS] || [];
    for (const keyword of categoryKeywords) {
      if (queryLower.includes(keyword.toLowerCase())) {
        score += 1;
      }
    }

    // Semantic keyword bonus
    for (const [semantic, keywords] of Object.entries(SEMANTIC_KEYWORDS)) {
      for (const keyword of keywords) {
        if (queryLower.includes(keyword.toLowerCase())) {
          score += 0.5;
        }
      }
    }

    return score;
  }

  /**
   * Search the knowledge base with caching
   */
  public search(query: string, options: SearchOptions = {}): SearchResult[] {
    const {
      categories = Object.keys(CATEGORY_KEYWORDS),
      maxResults = 10,
      minRelevanceScore = 0.5,
      includeGeorgian = true
    } = options;

    // Check cache first
    const cacheKey = cacheService.generateSearchKey(query, categories.join(','));
    const cachedResults = cacheService.get<SearchResult[]>(cacheKey);

    if (cachedResults) {
      console.log(`🔍 Cache hit for search: "${query}"`);
      return cachedResults.slice(0, maxResults);
    }

    console.log(`🔍 Searching knowledge base for: "${query}"`);

    const results: SearchResult[] = [];
    
    // Search in specified categories
    for (const category of categories) {
      const categoryItems = this.searchIndex.get(category) || [];
      
      for (const item of categoryItems) {
        const relevanceScore = this.calculateRelevanceScore(query, item);
        
        if (relevanceScore >= minRelevanceScore) {
          results.push({
            category,
            subcategory: item.subcategory,
            data: item.data,
            relevanceScore,
            source: `${category}.${item.key}`,
            summary: this.generateSummary(item.data, includeGeorgian)
          });
        }
      }
    }

    // Sort by relevance score (highest first)
    results.sort((a, b) => b.relevanceScore - a.relevanceScore);

    // Cache the results for future use (cache for 10 minutes)
    cacheService.set(cacheKey, results, 10 * 60 * 1000);

    // Limit results
    const limitedResults = results.slice(0, maxResults);

    console.log(`✅ Found ${limitedResults.length} relevant results`);

    return limitedResults;
  }

  /**
   * Generate a summary for search results
   */
  private generateSummary(data: any, includeGeorgian: boolean = true): string {
    let summary = '';
    
    // Primary name/title
    if (data.name) {
      summary += data.name;
      if (includeGeorgian && data.nameGeorgian) {
        summary += ` (${data.nameGeorgian})`;
      }
    } else if (data.title) {
      summary += data.title;
    }
    
    // Description
    if (data.description) {
      summary += `: ${data.description.substring(0, 150)}`;
      if (data.description.length > 150) summary += '...';
    }
    
    // Key details
    if (data.duration) summary += ` | Duration: ${data.duration}`;
    if (data.cost || data.tuitionFee) {
      const cost = data.cost || data.tuitionFee.annual;
      const currency = data.currency || data.tuitionFee?.currency || 'GEL';
      summary += ` | Cost: ${cost} ${currency}`;
    }
    if (data.location) summary += ` | Location: ${data.location}`;
    if (data.hours) summary += ` | Hours: ${data.hours}`;
    if (data.phone) summary += ` | Phone: ${data.phone}`;
    if (data.email) summary += ` | Email: ${data.email}`;
    
    return summary || 'No summary available';
  }

  /**
   * Get specific category data with caching
   */
  public getCategory(category: string): any {
    const cacheKey = cacheService.generateCategoryKey(category);
    const cachedData = cacheService.get(cacheKey);

    if (cachedData) {
      console.log(`📂 Cache hit for category: ${category}`);
      return cachedData;
    }

    const data = this.knowledgeBase[category] || null;

    if (data) {
      // Cache category data for 30 minutes
      cacheService.set(cacheKey, data, 30 * 60 * 1000);
      console.log(`📂 Category data cached: ${category}`);
    }

    return data;
  }

  /**
   * Get all available categories
   */
  public getCategories(): string[] {
    return Object.keys(this.knowledgeBase).filter(key => key !== 'metadata');
  }

  /**
   * Get metadata about the knowledge base
   */
  public getMetadata(): any {
    return this.knowledgeBase.metadata;
  }

  /**
   * Search for specific program information
   */
  public searchPrograms(query: string): SearchResult[] {
    return this.search(query, { 
      categories: ['academicPrograms'], 
      maxResults: 5,
      minRelevanceScore: 0.3 
    });
  }

  /**
   * Search for faculty information
   */
  public searchFaculty(query: string): SearchResult[] {
    return this.search(query, { 
      categories: ['faculty'], 
      maxResults: 5,
      minRelevanceScore: 0.3 
    });
  }

  /**
   * Search for admission information
   */
  public searchAdmissions(query: string): SearchResult[] {
    return this.search(query, { 
      categories: ['admissions'], 
      maxResults: 5,
      minRelevanceScore: 0.3 
    });
  }

  /**
   * Search for contact information
   */
  public searchContact(query: string): SearchResult[] {
    return this.search(query, { 
      categories: ['contact'], 
      maxResults: 5,
      minRelevanceScore: 0.3 
    });
  }

  /**
   * Get quick facts about the university with caching
   */
  public getQuickFacts(): any {
    const cacheKey = cacheService.generateQuickFactsKey();
    const cachedFacts = cacheService.get(cacheKey);

    if (cachedFacts) {
      console.log('⚡ Cache hit for quick facts');
      return cachedFacts;
    }

    const facts = {
      name: this.knowledgeBase.metadata.university,
      nameGeorgian: this.knowledgeBase.metadata.universityGeorgian,
      programs: Object.keys(this.knowledgeBase.academicPrograms.undergraduate).length +
                Object.keys(this.knowledgeBase.academicPrograms.graduate).length,
      mainPhone: this.knowledgeBase.contact.main.phone,
      mainEmail: this.knowledgeBase.contact.main.email,
      address: this.knowledgeBase.contact.main.address,
      addressGeorgian: this.knowledgeBase.contact.main.addressGeorgian
    };

    // Cache quick facts for 1 hour
    cacheService.set(cacheKey, facts, 60 * 60 * 1000);
    console.log('⚡ Quick facts cached');

    return facts;
  }
}

// Export singleton instance
export const knowledgeBaseService = new KnowledgeBaseService();
export default knowledgeBaseService;
