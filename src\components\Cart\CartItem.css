.cart-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  background: white;
  border: 1px solid var(--neutral-200);
  margin-bottom: var(--space-3);
  position: relative;
  transition: all var(--transition-fast);
}

.cart-item:last-child {
  margin-bottom: 0;
}

.cart-item:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--primary-200);
}

.cart-item.updating {
  opacity: 0.7;
  pointer-events: none;
}

.cart-item-image {
  flex-shrink: 0;
  width: 60px;
  height: 60px;
  border-radius: var(--radius-md);
  overflow: hidden;
  background: var(--neutral-100);
}

.cart-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cart-item-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  min-width: 0;
}

.cart-item-info {
  flex: 1;
}

.cart-item-name {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neutral-900);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.cart-item-category {
  margin: 0;
  font-size: 0.8rem;
  color: var(--neutral-500);
  margin-top: var(--space-1);
}

.cart-item-price {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--primary-600);
  margin-top: var(--space-1);
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  background: var(--neutral-100);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.quantity-btn {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: var(--neutral-600);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.quantity-btn:hover:not(:disabled) {
  background: var(--primary-50);
  color: var(--primary-600);
  box-shadow: var(--shadow-md);
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  min-width: 32px;
  text-align: center;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--neutral-900);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.remove-btn {
  width: 28px;
  height: 28px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--neutral-100);
  color: var(--neutral-500);
  transition: all var(--transition-fast);
}

.remove-btn:hover:not(:disabled) {
  background: var(--error);
  color: white;
  transform: scale(1.05);
}

.remove-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.cart-item-total {
  flex-shrink: 0;
  text-align: right;
  padding-top: var(--space-1);
}

.item-total-price {
  font-size: 1rem;
  font-weight: 700;
  color: var(--neutral-900);
}

.cart-item-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  backdrop-filter: blur(2px);
}

/* Responsive design */
@media (max-width: 480px) {
  .cart-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .cart-item-image {
    width: 100%;
    height: 120px;
  }
  
  .cart-item-controls {
    justify-content: space-between;
  }
  
  .cart-item-total {
    text-align: left;
    padding-top: 0;
  }
}
