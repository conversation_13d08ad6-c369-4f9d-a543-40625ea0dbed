/* University Chat Window Styles */
.chat-window {
  position: fixed;
  bottom: 20px;
  width: 360px;
  height: 520px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  animation: fadeIn 0.3s ease-out;
  z-index: 9999;
  max-height: calc(100vh - 40px);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@media (max-width: 768px) {
  .chat-window {
    width: calc(100% - 40px);
    height: 480px;
    bottom: 20px;
    border-radius: 12px;
  }
}

@media (max-width: 480px) {
  .chat-window {
    width: calc(100% - 20px);
    height: 440px;
    bottom: 20px;
    right: 10px !important;
    left: 10px !important;
    border-radius: 8px;
  }
}

@media (max-height: 600px) {
  .chat-window {
    height: calc(100vh - 60px);
    max-height: 400px;
  }
}
