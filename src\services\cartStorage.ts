import type { CartItem, CartStorageData } from '../types/cart';

const CART_STORAGE_KEY = 'premium-store-cart';
const CART_VERSION = '1.0.0';

/**
 * Cart storage service for persistent cart data
 */
export class CartStorageService {
  /**
   * Save cart items to localStorage
   */
  static async saveCart(items: CartItem[]): Promise<boolean> {
    try {
      const cartData: CartStorageData = {
        items,
        lastUpdated: Date.now(),
        version: CART_VERSION
      };

      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cartData));

      // Also sync to server if available (future enhancement)
      await this.syncToServer(cartData);

      return true;
    } catch (error) {
      console.error('Failed to save cart:', error);
      return false;
    }
  }

  /**
   * Load cart items from localStorage
   */
  static async loadCart(): Promise<CartItem[]> {
    try {
      const savedData = localStorage.getItem(CART_STORAGE_KEY);

      if (!savedData) {
        return [];
      }

      const cartData: CartStorageData = JSON.parse(savedData);

      // Check version compatibility
      if (cartData.version !== CART_VERSION) {
        console.warn('Cart version mismatch, clearing cart');
        await this.clearCart();
        return [];
      }

      // Validate cart items
      const validItems = cartData.items.filter(this.validateCartItem);

      // If some items were invalid, save the cleaned cart
      if (validItems.length !== cartData.items.length) {
        await this.saveCart(validItems);
      }

      return validItems;
    } catch (error) {
      console.error('Failed to load cart:', error);
      return [];
    }
  }

  /**
   * Clear all cart data
   */
  static async clearCart(): Promise<boolean> {
    try {
      localStorage.removeItem(CART_STORAGE_KEY);
      
      // Also clear from server if available
      await this.syncToServer({ items: [], lastUpdated: Date.now(), version: CART_VERSION });
      
      return true;
    } catch (error) {
      console.error('Failed to clear cart:', error);
      return false;
    }
  }

  /**
   * Get cart statistics
   */
  static getCartStats(): { itemCount: number; lastUpdated: number | null } {
    try {
      const savedData = localStorage.getItem(CART_STORAGE_KEY);
      
      if (!savedData) {
        return { itemCount: 0, lastUpdated: null };
      }

      const cartData: CartStorageData = JSON.parse(savedData);
      const totalItems = cartData.items.reduce((sum, item) => sum + item.quantity, 0);
      
      return {
        itemCount: totalItems,
        lastUpdated: cartData.lastUpdated
      };
    } catch (error) {
      console.error('Failed to get cart stats:', error);
      return { itemCount: 0, lastUpdated: null };
    }
  }

  /**
   * Validate a cart item
   */
  private static validateCartItem(item: any): item is CartItem {
    return (
      typeof item === 'object' &&
      typeof item.id === 'string' &&
      typeof item.productId === 'string' &&
      typeof item.name === 'string' &&
      typeof item.price === 'number' &&
      typeof item.imageUrl === 'string' &&
      typeof item.quantity === 'number' &&
      typeof item.addedAt === 'number' &&
      typeof item.category === 'string' &&
      typeof item.description === 'string' &&
      item.quantity > 0 &&
      item.price >= 0
    );
  }

  /**
   * Sync cart data to server (placeholder for future implementation)
   */
  private static async syncToServer(_cartData: CartStorageData): Promise<void> {
    // TODO: Implement server sync when backend is available
    // This would typically make an API call to save cart data to the server
    
    try {
      // Placeholder for server sync
      // await fetch('/api/cart/sync', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(cartData)
      // });
    } catch (error) {
      console.warn('Server sync failed, cart saved locally only:', error);
    }
  }

  /**
   * Export cart data for backup
   */
  static exportCart(): string | null {
    try {
      const savedData = localStorage.getItem(CART_STORAGE_KEY);
      return savedData;
    } catch (error) {
      console.error('Failed to export cart:', error);
      return null;
    }
  }

  /**
   * Import cart data from backup
   */
  static async importCart(cartDataString: string): Promise<boolean> {
    try {
      const cartData: CartStorageData = JSON.parse(cartDataString);
      
      // Validate the imported data
      if (!cartData.items || !Array.isArray(cartData.items)) {
        throw new Error('Invalid cart data format');
      }

      const validItems = cartData.items.filter(this.validateCartItem);
      
      return await this.saveCart(validItems);
    } catch (error) {
      console.error('Failed to import cart:', error);
      return false;
    }
  }
}
