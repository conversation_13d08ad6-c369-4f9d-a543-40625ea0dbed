import { BookOpen, Search, Clock, Download } from 'lucide-react';

const Library = () => {
  return (
    <div className="library-page">
      <section className="page-header">
        <div className="container">
          <h1>ბიბლიოთეკა / Library</h1>
          <p>ციფრული და ფიზიკური რესურსები</p>
          <p>Digital and physical resources</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="library-grid grid grid-2">
            <div className="library-card card">
              <BookOpen size={40} />
              <h3>კატალოგი / Catalog</h3>
              <p>ძიება ბიბლიოთეკის კატალოგში</p>
              <p>Search library catalog</p>
              <button className="btn btn-primary">ძიება / Search</button>
            </div>
            
            <div className="library-card card">
              <Download size={40} />
              <h3>ციფრული რესურსები / Digital Resources</h3>
              <p>ელექტრონული წიგნები და ჟურნალები</p>
              <p>E-books and journals</p>
              <button className="btn btn-primary">წვდომა / Access</button>
            </div>
            
            <div className="library-card card">
              <Search size={40} />
              <h3>კვლევითი დახმარება / Research Help</h3>
              <p>ბიბლიოთეკარების დახმარება</p>
              <p>Librarian assistance</p>
              <button className="btn btn-primary">დახმარება / Help</button>
            </div>
            
            <div className="library-card card">
              <Clock size={40} />
              <h3>სამუშაო საათები / Hours</h3>
              <p>ორშაბათი-პარასკევი: 8:00-20:00</p>
              <p>Monday-Friday: 8:00 AM - 8:00 PM</p>
              <p>შაბათი: 9:00-17:00</p>
              <p>Saturday: 9:00 AM - 5:00 PM</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Library;
