import axios from 'axios';
import knowledgeBaseService, { type SearchResult } from './knowledgeBaseService';

// Groq API configuration
const GROQ_API_KEY = '********************************************************';
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';
const MODEL = 'llama-3.3-70b-versatile';

// Enhanced system prompt with knowledge base integration
const SYSTEM_PROMPT = `You are <PERSON>, a comprehensive university assistant for Sokhumi State University (სოხუმის სახელმწიფო უნივერსიტეტი).

Your role is to provide detailed, accurate information about all aspects of university life using the comprehensive knowledge base available to you.

CORE CAPABILITIES:
- Academic Programs: Detailed course information, degree requirements, faculty, career opportunities
- Admissions: Requirements, deadlines, fees, scholarships, application processes
- Student Services: Housing, dining, health services, tutoring, library, clubs
- Campus Facilities: Buildings, technology, parking, accessibility
- Research: Centers, projects, opportunities, faculty research
- Events: Academic calendar, cultural events, orientation, graduation
- Policies: Academic regulations, conduct codes, grading systems
- Contact Information: Departments, faculty, emergency contacts

KNOWLEDGE BASE INTEGRATION:
When users ask specific questions, search the knowledge base to provide accurate, detailed information. Always cite your sources and provide specific details like:
- Exact costs, fees, and deadlines
- Specific contact information (phone, email, office locations)
- Detailed program requirements and course lists
- Faculty names, specializations, and contact details
- Precise schedules, hours, and locations

KEY UNIVERSITY INFORMATION:
- Location: Sokhumi, Georgia (სოხუმი, საქართველო)
- Focus: Education, Research, Innovation (განათლება • კვლევა • ინოვაცია)
- Motto: "სოხუმი არის ურთიერთბა" (Sokhumi is interaction)
- Main Phone: +995 443 123456
- Email: <EMAIL>

RESPONSE GUIDELINES:
- Provide specific, actionable information with exact details
- Include both Georgian and English information when available
- Give contact information for follow-up questions
- Suggest next steps for users (applications, visits, meetings)
- Be warm, professional, and encouraging
- If information isn't in the knowledge base, clearly state this and provide general guidance
- Always prioritize accuracy over assumptions

LANGUAGE SUPPORT:
- Respond in the language the user prefers (Georgian or English)
- Provide translations when helpful
- Use proper Georgian script and terminology

Remember: You have access to comprehensive, up-to-date university information. Use it to provide detailed, helpful responses that truly assist users with their university-related needs.`;

// Interface for chat messages
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// Maximum number of retries for API calls
const MAX_RETRIES = 2;

// Knowledge base search functions
const searchKnowledgeBase = (query: string, category?: string): SearchResult[] => {
  console.log(`🔍 Searching knowledge base: "${query}" in category: ${category || 'all'}`);

  if (category) {
    switch (category.toLowerCase()) {
      case 'programs':
      case 'academic':
        return knowledgeBaseService.searchPrograms(query);
      case 'faculty':
      case 'professor':
      case 'staff':
        return knowledgeBaseService.searchFaculty(query);
      case 'admissions':
      case 'admission':
      case 'apply':
        return knowledgeBaseService.searchAdmissions(query);
      case 'contact':
      case 'phone':
      case 'email':
        return knowledgeBaseService.searchContact(query);
      default:
        return knowledgeBaseService.search(query);
    }
  }

  return knowledgeBaseService.search(query);
};

const formatSearchResults = (results: SearchResult[]): string => {
  if (results.length === 0) {
    return "No specific information found in the knowledge base.";
  }

  let formatted = "📚 **Knowledge Base Information:**\n\n";

  results.slice(0, 3).forEach((result, index) => {
    formatted += `**${index + 1}. ${result.summary}**\n`;

    // Add specific details based on category
    if (result.category === 'academicPrograms' && result.data) {
      if (result.data.tuitionFee) {
        formatted += `💰 Tuition: ${result.data.tuitionFee.annual} ${result.data.tuitionFee.currency}\n`;
      }
      if (result.data.duration) {
        formatted += `⏱️ Duration: ${result.data.duration}\n`;
      }
      if (result.data.admissionRequirements?.minimumGPA) {
        formatted += `📊 Min GPA: ${result.data.admissionRequirements.minimumGPA}\n`;
      }
    }

    if (result.category === 'faculty' && result.data) {
      if (result.data.email) {
        formatted += `📧 Email: ${result.data.email}\n`;
      }
      if (result.data.phone) {
        formatted += `📞 Phone: ${result.data.phone}\n`;
      }
      if (result.data.office) {
        formatted += `🏢 Office: ${result.data.office}\n`;
      }
    }

    if (result.category === 'contact' && result.data) {
      if (result.data.phone) {
        formatted += `📞 Phone: ${result.data.phone}\n`;
      }
      if (result.data.email) {
        formatted += `📧 Email: ${result.data.email}\n`;
      }
      if (result.data.office) {
        formatted += `🏢 Office: ${result.data.office}\n`;
      }
    }

    formatted += `📍 Source: ${result.source}\n\n`;
  });

  if (results.length > 3) {
    formatted += `... and ${results.length - 3} more results available.\n\n`;
  }

  return formatted;
};

const detectQueryIntent = (query: string): { category?: string; intent: string } => {
  const queryLower = query.toLowerCase();

  // Program/Academic queries
  if (queryLower.includes('program') || queryLower.includes('course') || queryLower.includes('degree') ||
      queryLower.includes('study') || queryLower.includes('major') || queryLower.includes('კურსი') ||
      queryLower.includes('პროგრამა')) {
    return { category: 'programs', intent: 'academic_info' };
  }

  // Faculty queries
  if (queryLower.includes('professor') || queryLower.includes('faculty') || queryLower.includes('teacher') ||
      queryLower.includes('staff') || queryLower.includes('პროფესორი') || queryLower.includes('ფაკულტეტი')) {
    return { category: 'faculty', intent: 'faculty_info' };
  }

  // Admission queries
  if (queryLower.includes('admission') || queryLower.includes('apply') || queryLower.includes('requirement') ||
      queryLower.includes('deadline') || queryLower.includes('ჩარიცხვა') || queryLower.includes('განაცხადი')) {
    return { category: 'admissions', intent: 'admission_info' };
  }

  // Contact queries
  if (queryLower.includes('contact') || queryLower.includes('phone') || queryLower.includes('email') ||
      queryLower.includes('address') || queryLower.includes('კონტაქტი') || queryLower.includes('ტელეფონი')) {
    return { category: 'contact', intent: 'contact_info' };
  }

  // Cost/Fee queries
  if (queryLower.includes('cost') || queryLower.includes('fee') || queryLower.includes('tuition') ||
      queryLower.includes('price') || queryLower.includes('ღირებულება') || queryLower.includes('ფასი')) {
    return { intent: 'cost_info' };
  }

  // General info
  return { intent: 'general_info' };
};

// Enhanced function to get AI response with knowledge base integration
export const getUniversityAIResponse = async (
  userMessage: string,
  chatHistory: ChatMessage[] = []
): Promise<string> => {
  let retries = 0;

  while (retries <= MAX_RETRIES) {
    try {
      console.log(`🤖 Enhanced University AI Request (attempt ${retries + 1}):`, userMessage);

      // Step 1: Search knowledge base for relevant information
      const queryIntent = detectQueryIntent(userMessage);
      const searchResults = searchKnowledgeBase(userMessage, queryIntent.category);

      console.log(`🔍 Found ${searchResults.length} knowledge base results for intent: ${queryIntent.intent}`);

      // Step 2: Format knowledge base results
      const knowledgeContext = formatSearchResults(searchResults);

      // Step 3: Create enhanced prompt with knowledge base context
      let enhancedUserMessage = userMessage;

      if (searchResults.length > 0) {
        enhancedUserMessage = `User Question: ${userMessage}

${knowledgeContext}

Please provide a comprehensive response using the knowledge base information above. Include specific details like costs, contact information, requirements, and next steps when available. If the user asked in Georgian, respond in Georgian. If they asked in English, respond in English.`;
      } else {
        enhancedUserMessage = `User Question: ${userMessage}

Note: No specific information was found in the knowledge base for this query. Please provide general guidance and suggest contacting the university directly for specific information.

University Contact Information:
- Main Phone: +995 443 123456
- Email: <EMAIL>
- Address: University Street 1, Sokhumi, Georgia

If the user asked in Georgian, respond in Georgian. If they asked in English, respond in English.`;
      }

      // Step 4: Prepare the messages array with enhanced context
      const messages: ChatMessage[] = [
        { role: 'system', content: SYSTEM_PROMPT },
        ...chatHistory.slice(-4), // Keep last 4 messages for context (reduced to make room for knowledge base data)
        { role: 'user', content: enhancedUserMessage }
      ];

      console.log('📤 Sending enhanced request to Groq API:', {
        model: MODEL,
        messageCount: messages.length,
        hasKnowledgeBase: searchResults.length > 0,
        userMessage: userMessage.substring(0, 100) + '...'
      });

      // Step 5: Make the API request with a timeout
      const response = await axios.post(
        GROQ_API_URL,
        {
          model: MODEL,
          messages,
          temperature: 0.7,
          max_tokens: 800, // Increased for more detailed responses
        },
        {
          headers: {
            'Authorization': `Bearer ${GROQ_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      console.log('📥 Groq API Response Status:', response.status);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const aiResponse = response.data.choices[0].message.content;
        console.log('✅ Enhanced University AI Response received:', aiResponse.substring(0, 100) + '...');

        // Step 6: Add knowledge base attribution if results were used
        let finalResponse = aiResponse;
        if (searchResults.length > 0) {
          finalResponse += `\n\n---\n💡 *This information is from Sokhumi State University's official knowledge base. For the most current information or additional questions, please contact the university directly.*`;
        }

        return finalResponse;
      } else {
        throw new Error('Invalid response format from Groq API');
      }

    } catch (error: any) {
      console.error(`❌ University AI Error (attempt ${retries + 1}):`, error);

      retries++;

      if (retries > MAX_RETRIES) {
        console.error('🚨 Max retries exceeded for University AI request');
        
        // Return a helpful fallback message
        return `I apologize, but I'm experiencing technical difficulties right now. 

For immediate assistance, please contact:
- Main Office: +995 443 123456
- Email: <EMAIL>
- Visit our campus at Sokhumi, Georgia

You can also visit our website for more information about our programs and services.

Thank you for your patience, and I'll be back to help you soon!`;
      }

      // Wait before retrying (exponential backoff)
      const waitTime = Math.pow(2, retries) * 1000;
      console.log(`⏳ Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  // This should never be reached, but just in case
  return "I'm sorry, I'm unable to respond right now. Please try again later.";
};

// Function to format chat history for the API
export const formatChatHistoryForUniversity = (messages: any[]): ChatMessage[] => {
  return messages
    .filter(msg => msg.sender === 'user' || msg.sender === 'agent')
    .map(msg => ({
      role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
      content: msg.content
    }));
};

// Function to detect if message is about university topics
export const isUniversityRelated = (message: string): boolean => {
  const universityKeywords = [
    'university', 'უნივერსიტეტი', 'education', 'განათლება',
    'course', 'კურსი', 'program', 'პროგრამა', 'student', 'სტუდენტი',
    'admission', 'ჩარიცხვა', 'faculty', 'ფაკულტეტი', 'research', 'კვლევა',
    'campus', 'კამპუსი', 'library', 'ბიბლიოთეკა', 'degree', 'ხარისხი',
    'sokhumi', 'სოხუმი', 'georgia', 'საქართველო'
  ];

  const lowerMessage = message.toLowerCase();
  return universityKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
};

// Function to get university-specific suggestions
export const getUniversitySuggestions = (userInput: string): string[] => {
  const input = userInput.toLowerCase();
  
  if (input.includes('program') || input.includes('course') || input.includes('კურსი') || input.includes('პროგრამა')) {
    return [
      'What undergraduate programs do you offer?',
      'Tell me about graduate programs',
      'What are the admission requirements?',
      'How do I apply for a program?'
    ];
  }
  
  if (input.includes('admission') || input.includes('apply') || input.includes('ჩარიცხვა') || input.includes('განაცხადი')) {
    return [
      'What documents do I need for admission?',
      'When is the application deadline?',
      'What are the tuition fees?',
      'Are scholarships available?'
    ];
  }
  
  if (input.includes('research') || input.includes('კვლევა')) {
    return [
      'What research opportunities are available?',
      'How can I get involved in research?',
      'Tell me about research facilities',
      'Who are the research faculty?'
    ];
  }
  
  if (input.includes('campus') || input.includes('facility') || input.includes('კამპუსი') || input.includes('შენობა')) {
    return [
      'Where is the university located?',
      'What facilities are available on campus?',
      'How do I get to the university?',
      'Tell me about the library'
    ];
  }
  
  // Default suggestions
  return [
    'Tell me about Sokhumi State University',
    'What programs do you offer?',
    'How do I apply for admission?',
    'What research opportunities are available?'
  ];
};

// Function to validate API key
export const validateAPIKey = (): boolean => {
  const isValid = GROQ_API_KEY && GROQ_API_KEY.length > 0;
  console.log('🔑 API Key validation:', isValid ? 'Valid' : 'Invalid');
  return isValid;
};

// Function to get API status
export const getAPIStatus = async (): Promise<{ status: 'online' | 'offline'; message: string }> => {
  try {
    await axios.post(
      GROQ_API_URL,
      {
        model: MODEL,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
      },
      {
        headers: {
          'Authorization': `Bearer ${GROQ_API_KEY}`,
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      }
    );

    return {
      status: 'online',
      message: 'University AI service is operational'
    };
  } catch (error) {
    return {
      status: 'offline',
      message: 'University AI service is currently unavailable'
    };
  }
};

// Quick access functions for common university information
export const getQuickUniversityInfo = () => {
  return knowledgeBaseService.getQuickFacts();
};

export const getUniversityPrograms = () => {
  const programs = knowledgeBaseService.getCategory('academicPrograms');
  return {
    undergraduate: Object.keys(programs?.undergraduate || {}),
    graduate: Object.keys(programs?.graduate || {}),
    total: Object.keys(programs?.undergraduate || {}).length + Object.keys(programs?.graduate || {}).length
  };
};

export const getUniversityContact = () => {
  const contact = knowledgeBaseService.getCategory('contact');
  return {
    main: contact?.main || {},
    departments: contact?.departments || {},
    emergency: contact?.emergencyContacts || {}
  };
};

export const searchUniversityInfo = (query: string, category?: string) => {
  return searchKnowledgeBase(query, category);
};

// Function to get knowledge base statistics
export const getKnowledgeBaseStats = () => {
  const categories = knowledgeBaseService.getCategories();
  const metadata = knowledgeBaseService.getMetadata();

  return {
    categories: categories.length,
    lastUpdated: metadata.lastUpdated,
    version: metadata.version,
    availableCategories: categories
  };
};
