import axios from 'axios';

// Groq API configuration
const GROQ_API_KEY = '********************************************************';
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';
const MODEL = 'llama-3.3-70b-versatile';

// System prompt for university assistant
const SYSTEM_PROMPT = `You are a helpful university assistant for Sokhumi State University (სოხუმის სახელმწიფო უნივერსიტეტი).

Your role is to help students, faculty, and visitors with:
- Information about academic programs and courses
- Admission requirements and procedures
- University facilities and services
- Research opportunities and projects
- Student life and campus activities
- Contact information and directions
- General university policies and procedures

Key information about Sokhumi State University:
- Located in Sokhumi, Georgia
- Offers undergraduate and graduate programs
- Focus areas: Education, Research, Innovation (განათლება • კვლევა • ინოვაცია)
- University motto: "სოხუმი არის ურთიერთბა" (Sokhumi is interaction)

Guidelines for responses:
- Be helpful, professional, and informative
- Provide accurate information about university services
- If you don't know specific details, direct users to appropriate contacts
- Be supportive and encouraging to prospective and current students
- Maintain a warm, welcoming tone that reflects Georgian hospitality
- Keep responses concise but comprehensive
- Always prioritize student success and well-being

When discussing programs or services, provide relevant details and next steps for the user.`;

// Interface for chat messages
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// Maximum number of retries for API calls
const MAX_RETRIES = 2;

// Function to get AI response from Groq
export const getUniversityAIResponse = async (
  userMessage: string,
  chatHistory: ChatMessage[] = []
): Promise<string> => {
  let retries = 0;

  while (retries <= MAX_RETRIES) {
    try {
      console.log(`🤖 University AI Request (attempt ${retries + 1}):`, userMessage);

      // Prepare the messages array with system prompt and chat history
      const messages: ChatMessage[] = [
        { role: 'system', content: SYSTEM_PROMPT },
        ...chatHistory.slice(-6), // Keep last 6 messages for context
        { role: 'user', content: userMessage }
      ];

      console.log('📤 Sending to Groq API:', {
        model: MODEL,
        messageCount: messages.length,
        userMessage: userMessage.substring(0, 100) + '...'
      });

      // Make the API request with a timeout
      const response = await axios.post(
        GROQ_API_URL,
        {
          model: MODEL,
          messages,
          temperature: 0.7,
          max_tokens: 500,
        },
        {
          headers: {
            'Authorization': `Bearer ${GROQ_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      console.log('📥 Groq API Response Status:', response.status);

      if (response.data && response.data.choices && response.data.choices.length > 0) {
        const aiResponse = response.data.choices[0].message.content;
        console.log('✅ University AI Response received:', aiResponse.substring(0, 100) + '...');
        return aiResponse;
      } else {
        throw new Error('Invalid response format from Groq API');
      }

    } catch (error: any) {
      console.error(`❌ University AI Error (attempt ${retries + 1}):`, error);

      retries++;

      if (retries > MAX_RETRIES) {
        console.error('🚨 Max retries exceeded for University AI request');
        
        // Return a helpful fallback message
        return `I apologize, but I'm experiencing technical difficulties right now. 

For immediate assistance, please contact:
- Main Office: +995 443 123456
- Email: <EMAIL>
- Visit our campus at Sokhumi, Georgia

You can also visit our website for more information about our programs and services.

Thank you for your patience, and I'll be back to help you soon!`;
      }

      // Wait before retrying (exponential backoff)
      const waitTime = Math.pow(2, retries) * 1000;
      console.log(`⏳ Waiting ${waitTime}ms before retry...`);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }

  // This should never be reached, but just in case
  return "I'm sorry, I'm unable to respond right now. Please try again later.";
};

// Function to format chat history for the API
export const formatChatHistoryForUniversity = (messages: any[]): ChatMessage[] => {
  return messages
    .filter(msg => msg.sender === 'user' || msg.sender === 'agent')
    .map(msg => ({
      role: msg.sender === 'user' ? 'user' as const : 'assistant' as const,
      content: msg.content
    }));
};

// Function to detect if message is about university topics
export const isUniversityRelated = (message: string): boolean => {
  const universityKeywords = [
    'university', 'უნივერსიტეტი', 'education', 'განათლება',
    'course', 'კურსი', 'program', 'პროგრამა', 'student', 'სტუდენტი',
    'admission', 'ჩარიცხვა', 'faculty', 'ფაკულტეტი', 'research', 'კვლევა',
    'campus', 'კამპუსი', 'library', 'ბიბლიოთეკა', 'degree', 'ხარისხი',
    'sokhumi', 'სოხუმი', 'georgia', 'საქართველო'
  ];

  const lowerMessage = message.toLowerCase();
  return universityKeywords.some(keyword => lowerMessage.includes(keyword.toLowerCase()));
};

// Function to get university-specific suggestions
export const getUniversitySuggestions = (userInput: string): string[] => {
  const input = userInput.toLowerCase();
  
  if (input.includes('program') || input.includes('course') || input.includes('კურსი') || input.includes('პროგრამა')) {
    return [
      'What undergraduate programs do you offer?',
      'Tell me about graduate programs',
      'What are the admission requirements?',
      'How do I apply for a program?'
    ];
  }
  
  if (input.includes('admission') || input.includes('apply') || input.includes('ჩარიცხვა') || input.includes('განაცხადი')) {
    return [
      'What documents do I need for admission?',
      'When is the application deadline?',
      'What are the tuition fees?',
      'Are scholarships available?'
    ];
  }
  
  if (input.includes('research') || input.includes('კვლევა')) {
    return [
      'What research opportunities are available?',
      'How can I get involved in research?',
      'Tell me about research facilities',
      'Who are the research faculty?'
    ];
  }
  
  if (input.includes('campus') || input.includes('facility') || input.includes('კამპუსი') || input.includes('შენობა')) {
    return [
      'Where is the university located?',
      'What facilities are available on campus?',
      'How do I get to the university?',
      'Tell me about the library'
    ];
  }
  
  // Default suggestions
  return [
    'Tell me about Sokhumi State University',
    'What programs do you offer?',
    'How do I apply for admission?',
    'What research opportunities are available?'
  ];
};

// Function to validate API key
export const validateAPIKey = (): boolean => {
  const isValid = GROQ_API_KEY && GROQ_API_KEY.length > 0 && GROQ_API_KEY !== 'your-api-key-here';
  console.log('🔑 API Key validation:', isValid ? 'Valid' : 'Invalid');
  return isValid;
};

// Function to get API status
export const getAPIStatus = async (): Promise<{ status: 'online' | 'offline'; message: string }> => {
  try {
    const response = await axios.post(
      GROQ_API_URL,
      {
        model: MODEL,
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 10,
      },
      {
        headers: {
          'Authorization': `Bearer ${GROQ_API_KEY}`,
          'Content-Type': 'application/json',
        },
        timeout: 10000,
      }
    );

    return {
      status: 'online',
      message: 'University AI service is operational'
    };
  } catch (error) {
    return {
      status: 'offline',
      message: 'University AI service is currently unavailable'
    };
  }
};
