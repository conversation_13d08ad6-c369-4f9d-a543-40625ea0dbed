/* University Chat Input Styles */
.chat-input-container {
  display: flex;
  align-items: flex-end;
  padding: 12px 16px;
  border-top: 1px solid rgba(30, 64, 175, 0.1);
  background-color: white;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
  position: relative;
}

.chat-input {
  flex: 1;
  border: 1px solid rgba(30, 64, 175, 0.2);
  border-radius: 24px;
  padding: 12px 16px;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-size: 14px;
  resize: none;
  max-height: 120px;
  background-color: #f8fafc;
  color: #1f2937;
  transition: border-color 0.2s;
  scrollbar-width: thin;
  scrollbar-color: rgba(30, 64, 175, 0.2) transparent;
  position: relative;
}

/* Tooltip for paste hint */
.chat-input-container::after {
  content: "რჩევა: შეგიძლიათ სურათები პირდაპირ ჩასვათ";
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  opacity: 0;
  transition: opacity 0.3s, top 0.3s;
  pointer-events: none;
  white-space: nowrap;
  z-index: 1000;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.chat-input:focus + .chat-input-attachment,
.chat-input:focus + input + .chat-input-attachment {
  opacity: 1;
}

.chat-input:focus ~ button {
  opacity: 1;
}

.chat-input-container:hover::after {
  opacity: 0.9;
  top: -25px;
}

/* Custom scrollbar for WebKit browsers */
.chat-input::-webkit-scrollbar {
  width: 4px;
}

.chat-input::-webkit-scrollbar-track {
  background: transparent;
}

.chat-input::-webkit-scrollbar-thumb {
  background-color: rgba(30, 64, 175, 0.2);
  border-radius: 4px;
}

.chat-input::-webkit-scrollbar-thumb:hover {
  background-color: rgba(30, 64, 175, 0.3);
}

.chat-input:focus {
  outline: none;
  border-color: #1e40af;
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.chat-input::placeholder {
  color: rgba(107, 114, 128, 0.7);
}

.chat-input-attachment,
.chat-input-send {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  margin-left: 8px;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.2s;
}

.chat-input-attachment {
  background-color: transparent;
  color: #6b7280;
  opacity: 0.7;
}

.chat-input-attachment:hover {
  background-color: rgba(30, 64, 175, 0.1);
  color: #1e40af;
  opacity: 1;
  transform: scale(1.05);
}

.chat-input-send {
  background-color: #1e40af;
  color: white;
}

.chat-input-send:hover:not(:disabled) {
  background-color: #1d4ed8;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

.chat-input-send:disabled {
  cursor: not-allowed;
  background-color: #9ca3af;
  transform: none;
  box-shadow: none;
}

.chat-focus-visible:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

@media (max-width: 768px) {
  .chat-input-container {
    padding: 8px 12px;
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
  }

  .chat-input {
    padding: 10px 14px;
    font-size: 14px;
    border-radius: 20px;
  }

  .chat-input-attachment,
  .chat-input-send {
    width: 36px;
    height: 36px;
  }

  .chat-input-container::after {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .chat-input-container {
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
  }

  .chat-input {
    border-radius: 18px;
  }
}
