import { MapPin, Phone, Mail, Clock } from 'lucide-react';

const Contact = () => {
  return (
    <div className="contact-page">
      <section className="page-header">
        <div className="container">
          <h1>კონტაქტი / Contact</h1>
          <p>დაგვიკავშირდით ნებისმიერ დროს</p>
          <p>Contact us anytime</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="contact-grid grid grid-2">
            <div className="contact-info">
              <h2>საკონტაქტო ინფორმაცია / Contact Information</h2>
              
              <div className="contact-item">
                <MapPin size={24} />
                <div>
                  <h4>მისამართი / Address</h4>
                  <p>თბილისი, საქართველო</p>
                  <p>Tbilisi, Georgia</p>
                </div>
              </div>

              <div className="contact-item">
                <Phone size={24} />
                <div>
                  <h4>ტელეფონი / Phone</h4>
                  <p>+995 32 2 XX XX XX</p>
                  <p>+995 32 2 XX XX XX (ფაქსი / Fax)</p>
                </div>
              </div>

              <div className="contact-item">
                <Mail size={24} />
                <div>
                  <h4>ელ. ფოსტა / Email</h4>
                  <p><EMAIL></p>
                  <p><EMAIL></p>
                </div>
              </div>

              <div className="contact-item">
                <Clock size={24} />
                <div>
                  <h4>სამუშაო საათები / Working Hours</h4>
                  <p>ორშაბათი - პარასკევი: 9:00 - 18:00</p>
                  <p>Monday - Friday: 9:00 AM - 6:00 PM</p>
                  <p>შაბათი: 10:00 - 14:00</p>
                  <p>Saturday: 10:00 AM - 2:00 PM</p>
                </div>
              </div>
            </div>

            <div className="contact-form">
              <h2>გამოგვიგზავნეთ შეტყობინება / Send us a Message</h2>
              <form className="form">
                <div className="form-group">
                  <label htmlFor="name">სახელი / Name *</label>
                  <input type="text" id="name" name="name" required />
                </div>
                
                <div className="form-group">
                  <label htmlFor="email">ელ. ფოსტა / Email *</label>
                  <input type="email" id="email" name="email" required />
                </div>
                
                <div className="form-group">
                  <label htmlFor="phone">ტელეფონი / Phone</label>
                  <input type="tel" id="phone" name="phone" />
                </div>
                
                <div className="form-group">
                  <label htmlFor="subject">თემა / Subject *</label>
                  <input type="text" id="subject" name="subject" required />
                </div>
                
                <div className="form-group">
                  <label htmlFor="message">შეტყობინება / Message *</label>
                  <textarea id="message" name="message" rows={5} required></textarea>
                </div>
                
                <button type="submit" className="btn btn-primary">
                  გაგზავნა / Send Message
                </button>
              </form>
            </div>
          </div>
        </div>
      </section>

      <section className="section section-alt">
        <div className="container">
          <div className="section-header">
            <h2>რუკა / Map</h2>
            <p>ჩვენი მდებარეობა / Our Location</p>
          </div>
          <div className="map-container">
            <div className="map-placeholder">
              <MapPin size={60} />
              <p>რუკა ჩაიტვირთება მალე / Map will load soon</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Contact;
