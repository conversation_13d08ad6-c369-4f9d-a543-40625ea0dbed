import { FileText, Calendar, CheckCircle, Users } from 'lucide-react';

const Admissions = () => {
  return (
    <div className="admissions-page">
      <section className="page-header">
        <div className="container">
          <h1>მიღება / Admissions</h1>
          <p>შემოუერთდით ჩვენს უნივერსიტეტს</p>
          <p>Join our university</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2>მიღების პროცესი / Admission Process</h2>
          </div>
          <div className="process-grid grid grid-4">
            <div className="process-card card">
              <FileText size={40} />
              <h3>განაცხადი / Application</h3>
              <p>შეავსეთ ონლაინ განაცხადი</p>
              <p>Complete online application</p>
            </div>
            <div className="process-card card">
              <CheckCircle size={40} />
              <h3>დოკუმენტები / Documents</h3>
              <p>წარადგინეთ საჭირო დოკუმენტები</p>
              <p>Submit required documents</p>
            </div>
            <div className="process-card card">
              <Users size={40} />
              <h3>გამოცდა / Exam</h3>
              <p>ჩააბარეთ შესაბამისი გამოცდები</p>
              <p>Take relevant exams</p>
            </div>
            <div className="process-card card">
              <Calendar size={40} />
              <h3>რეგისტრაცია / Registration</h3>
              <p>დარეგისტრირდით კურსებზე</p>
              <p>Register for courses</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Admissions;
