/* University Chat Bubble Styles */
.chat-bubble {
  position: fixed;
  bottom: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #1e40af; /* University blue */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 9999;
}

.chat-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
  background-color: #1d4ed8;
}

.chat-bubble:active {
  transform: scale(0.95);
}

.chat-bubble.has-notification {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
  }
  50% {
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
  }
}

.chat-bubble-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-notification {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f59e0b; /* University amber */
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(245, 158, 11, 0.3);
  border: 2px solid white;
}

.chat-focus-visible:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

@media (max-width: 768px) {
  .chat-bubble {
    width: 50px;
    height: 50px;
    bottom: 16px;
  }
  
  .chat-bubble-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .chat-notification {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .chat-bubble {
    width: 45px;
    height: 45px;
    bottom: 12px;
  }
  
  .chat-bubble-icon svg {
    width: 18px;
    height: 18px;
  }
}
