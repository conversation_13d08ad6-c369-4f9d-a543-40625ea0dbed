import type { FC } from 'react';
import type { Message } from '../../types/chat';
import { formatTime } from '../../utils/time';
import { useChat } from '../../context/ChatContext';
import './MessageItem.css';

interface MessageItemProps {
  message: Message;
}

const MessageItem: FC<MessageItemProps> = ({ message }) => {
  const { config } = useChat();
  const isUser = message.sender === 'user';

  // Process message content to improve display
  const processMessageContent = (content: string) => {
    // Replace multiple consecutive spaces with a single space
    let processed = content.replace(/\s+/g, ' ');

    // Replace multiple consecutive line breaks with a single line break
    processed = processed.replace(/\n{3,}/g, '\n\n');

    // Trim leading and trailing whitespace
    return processed.trim();
  };

  // Check if the message is a single line (no line breaks)
  const isSingleLine = !message.content.includes('\n') && message.content.length < 25;

  // Check if the message has an image
  const hasImage = !!message.image;

  // Status icon based on message status
  const renderStatusIcon = () => {
    if (!isUser) return null;

    switch (message.status) {
      case 'sending':
        return (
          <svg className="message-status-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2" strokeDasharray="2 2" />
          </svg>
        );
      case 'sent':
        return (
          <svg className="message-status-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5 12L10 17L19 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        );
      case 'delivered':
        return (
          <svg className="message-status-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 12L7 17L16 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8 12L13 17L22 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        );
      case 'read':
        return (
          <svg className="message-status-icon message-status-read" width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M2 12L7 17L16 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            <path d="M8 12L13 17L22 8" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
          </svg>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`message-item ${isUser ? 'message-user' : 'message-agent'}`}>
      {!isUser && (
        <div className="message-avatar">
          {config.agentAvatar ? (
            <img src={config.agentAvatar} alt={config.agentName} />
          ) : (
            <div className="message-avatar-placeholder">
              🎓
            </div>
          )}
        </div>
      )}
      <div
        className="message-bubble"
        style={{
          backgroundColor: isUser ? (config.theme?.primaryColor || '#1e40af') : (config.theme?.secondaryColor || '#f59e0b'),
          color: isUser ? '#ffffff' : (config.theme?.textColor || '#1f2937')
        }}
      >
        {hasImage && (
          <div className="message-image-container">
            <img
              src={message.image?.url}
              alt={message.image?.name || "Attached image"}
              className="message-image"
              onClick={() => window.open(message.image?.url, '_blank')}
            />
            <div className="message-image-name">{message.image?.name}</div>
          </div>
        )}
        {message.content && (
          <div className={`message-content ${isSingleLine ? 'single-line' : ''}`}>
            {processMessageContent(message.content)}
          </div>
        )}
        <div className="message-meta">
          <span className="message-time">{formatTime(message.timestamp)}</span>
          {renderStatusIcon()}
        </div>
      </div>
      {isUser && (
        <div className="message-avatar">
          {config.userAvatar ? (
            <img src={config.userAvatar} alt="You" />
          ) : (
            <div className="message-avatar-placeholder user-avatar">
              👤
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MessageItem;
