import { Microscope, BookOpen, Users, Award } from 'lucide-react';

const Research = () => {
  return (
    <div className="research-page">
      <section className="page-header">
        <div className="container">
          <h1>კვლევა / Research</h1>
          <p>სამეცნიერო კვლევა და ინოვაციები</p>
          <p>Scientific research and innovations</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="research-grid grid grid-2">
            <div className="research-card card">
              <Microscope size={40} />
              <h3>კვლევითი ცენტრები / Research Centers</h3>
              <p>თანამედროვე კვლევითი ლაბორატორიები</p>
              <p>Modern research laboratories</p>
            </div>
            <div className="research-card card">
              <BookOpen size={40} />
              <h3>პუბლიკაციები / Publications</h3>
              <p>სამეცნიერო ნაშრომები და სტატიები</p>
              <p>Scientific papers and articles</p>
            </div>
            <div className="research-card card">
              <Users size={40} />
              <h3>თანამშრომლობა / Collaboration</h3>
              <p>საერთაშორისო კვლევითი პროექტები</p>
              <p>International research projects</p>
            </div>
            <div className="research-card card">
              <Award size={40} />
              <h3>გრანტები / Grants</h3>
              <p>კვლევითი დაფინანსება</p>
              <p>Research funding</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Research;
