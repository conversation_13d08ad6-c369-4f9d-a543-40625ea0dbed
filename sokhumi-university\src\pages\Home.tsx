import { Link } from 'react-router-dom';
import { GraduationCap, Users, BookOpen, Award, Calendar, ArrowRight } from 'lucide-react';

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-background">
          <div className="container">
            <div className="hero-content">
              <h1 className="hero-title">
                მოგესალმებით სოხუმის სახელმწიფო უნივერსიტეტში
              </h1>
              <h2 className="hero-subtitle">
                Welcome to Sokhumi State University
              </h2>
              <p className="hero-description">
                ჩვენი უნივერსიტეტი არის წამყვანი საგანმანათლებლო დაწესებულება, 
                რომელიც უზრუნველყოფს მაღალი ხარისხის განათლებას და ხელს უწყობს 
                სტუდენტების პროფესიულ და პიროვნულ განვითარებას.
              </p>
              <p className="hero-description">
                Our university is a leading educational institution providing 
                high-quality education and fostering professional and personal 
                development of our students.
              </p>
              <div className="hero-actions">
                <Link to="/admissions" className="btn btn-primary">
                  მიღება / Apply Now
                </Link>
                <Link to="/about" className="btn btn-outline">
                  ჩვენს შესახებ / Learn More
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section section">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <Users size={40} />
              </div>
              <div className="stat-content">
                <h3>5,000+</h3>
                <p>სტუდენტი / Students</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <GraduationCap size={40} />
              </div>
              <div className="stat-content">
                <h3>200+</h3>
                <p>პროფესორი / Faculty</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <BookOpen size={40} />
              </div>
              <div className="stat-content">
                <h3>50+</h3>
                <p>პროგრამა / Programs</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Award size={40} />
              </div>
              <div className="stat-content">
                <h3>25+</h3>
                <p>წლიანი გამოცდილება / Years of Excellence</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Programs */}
      <section className="featured-programs section section-alt">
        <div className="container">
          <div className="section-header">
            <h2>ჩვენი პროგრამები / Our Programs</h2>
            <p>აღმოაჩინეთ ჩვენი მრავალფეროვანი აკადემიური პროგრამები</p>
            <p>Discover our diverse range of academic programs</p>
          </div>
          <div className="programs-grid grid grid-3">
            <div className="program-card card">
              <h3>ბიზნეს ადმინისტრირება</h3>
              <h4>Business Administration</h4>
              <p>თანამედროვე ბიზნეს გარემოში წარმატებისთვის საჭირო ცოდნა და უნარები</p>
              <p>Knowledge and skills needed for success in modern business environment</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა / Learn More <ArrowRight size={16} />
              </Link>
            </div>
            <div className="program-card card">
              <h3>ინფორმაციული ტექნოლოგიები</h3>
              <h4>Information Technology</h4>
              <p>თანამედროვე IT ტექნოლოგიები და პროგრამირების უნარები</p>
              <p>Modern IT technologies and programming skills</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა / Learn More <ArrowRight size={16} />
              </Link>
            </div>
            <div className="program-card card">
              <h3>იურისპრუდენცია</h3>
              <h4>Law</h4>
              <p>სამართლებრივი განათლება და იურიდიული პრაქტიკა</p>
              <p>Legal education and juridical practice</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა / Learn More <ArrowRight size={16} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* News & Events */}
      <section className="news-events section">
        <div className="container">
          <div className="section-header">
            <h2>სიახლეები და ღონისძიებები / News & Events</h2>
            <p>იყავით კურსში უნივერსიტეტის ცხოვრების</p>
            <p>Stay updated with university life</p>
          </div>
          <div className="news-grid grid grid-2">
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-15</span>
              </div>
              <h3>ახალი აკადემიური წლის დაწყება</h3>
              <h4>New Academic Year Begins</h4>
              <p>
                ჩვენ გვიხარია ახალი აკადემიური წლის დაწყება და ახალი სტუდენტების მიღება...
              </p>
              <p>
                We are excited to begin the new academic year and welcome new students...
              </p>
              <Link to="/news" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </Link>
            </article>
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-10</span>
              </div>
              <h3>კვლევითი კონფერენცია</h3>
              <h4>Research Conference</h4>
              <p>
                უნივერსიტეტში ჩატარდება საერთაშორისო კვლევითი კონფერენცია...
              </p>
              <p>
                The university will host an international research conference...
              </p>
              <Link to="/news" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </Link>
            </article>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="cta-section section section-alt">
        <div className="container">
          <div className="cta-content">
            <h2>მზად ხართ დაიწყოთ თქვენი განათლების მოგზაურობა?</h2>
            <h3>Ready to Start Your Educational Journey?</h3>
            <p>
              შემოუერთდით ჩვენს უნივერსიტეტს და გახდით ჩვენი აკადემიური საზოგადოების ნაწილი
            </p>
            <p>
              Join our university and become part of our academic community
            </p>
            <div className="cta-actions">
              <Link to="/admissions" className="btn btn-primary">
                განაცხადის შეტანა / Apply Now
              </Link>
              <Link to="/contact" className="btn btn-secondary">
                კონტაქტი / Contact Us
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
