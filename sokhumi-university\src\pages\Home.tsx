import { Link } from 'react-router-dom';
import { GraduationCap, Users, BookOpen, Award, Calendar, ArrowRight } from 'lucide-react';

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-background">
          <div className="container-content">
            <div className="hero-content">
              <h1 className="hero-title">
                მოგესალმებით სოხუმის სახელმწიფო უნივერსიტეტში
              </h1>
              <h2 className="hero-subtitle">
                განათლება • კვლევა • ინოვაცია
              </h2>
              <p className="hero-description">
                ჩვენი უნივერსიტეტი არის წამყვანი საგანმანათლებლო დაწესებულება,
                რომელიც უზრუნველყოფს მაღალი ხარისხის განათლებას და ხელს უწყობს
                სტუდენტების პროფესიულ და პიროვნულ განვითარებას.
              </p>
              <div className="hero-actions">
                <Link to="/admissions" className="btn btn-primary">
                  მიღება
                </Link>
                <Link to="/about" className="btn btn-outline">
                  ჩვენს შესახებ
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="stats-section section">
        <div className="container-content">
          <div className="stats-grid">
            <div className="stat-card">
              <div className="stat-icon">
                <Users size={40} />
              </div>
              <div className="stat-content">
                <h3>5,000+</h3>
                <p>სტუდენტი</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <GraduationCap size={40} />
              </div>
              <div className="stat-content">
                <h3>200+</h3>
                <p>პროფესორი</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <BookOpen size={40} />
              </div>
              <div className="stat-content">
                <h3>50+</h3>
                <p>პროგრამა</p>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">
                <Award size={40} />
              </div>
              <div className="stat-content">
                <h3>25+</h3>
                <p>წლიანი გამოცდილება</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Programs */}
      <section className="featured-programs section section-alt">
        <div className="container-content">
          <div className="section-header">
            <h2>ჩვენი პროგრამები</h2>
            <p>აღმოაჩინეთ ჩვენი მრავალფეროვანი აკადემიური პროგრამები</p>
          </div>
          <div className="programs-grid grid grid-3">
            <div className="program-card card">
              <h3>ბიზნეს ადმინისტრირება</h3>
              <p>თანამედროვე ბიზნეს გარემოში წარმატებისთვის საჭირო ცოდნა და უნარები</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა <ArrowRight size={16} />
              </Link>
            </div>
            <div className="program-card card">
              <h3>ინფორმაციული ტექნოლოგიები</h3>
              <p>თანამედროვე IT ტექნოლოგიები და პროგრამირების უნარები</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა <ArrowRight size={16} />
              </Link>
            </div>
            <div className="program-card card">
              <h3>იურისპრუდენცია</h3>
              <p>სამართლებრივი განათლება და იურიდიული პრაქტიკა</p>
              <Link to="/academics" className="program-link">
                მეტის ნახვა <ArrowRight size={16} />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* News & Events */}
      <section className="news-events section">
        <div className="container-content">
          <div className="section-header">
            <h2>სიახლეები და ღონისძიებები</h2>
            <p>იყავით კურსში უნივერსიტეტის ცხოვრების</p>
          </div>
          <div className="news-grid grid grid-2">
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-15</span>
              </div>
              <h3>ახალი აკადემიური წლის დაწყება</h3>
              <p>
                ჩვენ გვიხარია ახალი აკადემიური წლის დაწყება და ახალი სტუდენტების მიღება...
              </p>
              <Link to="/news" className="news-link">
                სრულად წაკითხვა <ArrowRight size={16} />
              </Link>
            </article>
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-10</span>
              </div>
              <h3>კვლევითი კონფერენცია</h3>
              <p>
                უნივერსიტეტში ჩატარდება საერთაშორისო კვლევითი კონფერენცია...
              </p>
              <Link to="/news" className="news-link">
                სრულად წაკითხვა <ArrowRight size={16} />
              </Link>
            </article>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="cta-section section section-alt">
        <div className="container-content">
          <div className="cta-content">
            <h2>მზად ხართ დაიწყოთ თქვენი განათლების მოგზაურობა?</h2>
            <p>
              შემოუერთდით ჩვენს უნივერსიტეტს და გახდით ჩვენი აკადემიური საზოგადოების ნაწილი
            </p>
            <div className="cta-actions">
              <Link to="/admissions" className="btn btn-primary">
                განაცხადის შეტანა
              </Link>
              <Link to="/contact" className="btn btn-secondary">
                კონტაქტი
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
