import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import type { ReactNode } from 'react';
import type { CartContextType, CartState, CartItem } from '../types/cart';
import { CartStorageService } from '../services/cartStorage';
import { allProducts } from '../data/products';
import { v4 as uuidv4 } from 'uuid';

// Cart reducer actions
type CartAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ITEMS'; payload: CartItem[] }
  | { type: 'LOAD_ITEMS'; payload: CartItem[] }
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'REMOVE_MULTIPLE_ITEMS'; payload: string[] }
  | { type: 'UPDATE_QUANTITY'; payload: { productId: string; quantity: number } }
  | { type: 'CLEAR_CART' };

// Initial cart state
const initialState: CartState = {
  items: [],
  totalItems: 0,
  totalPrice: 0,
  isLoading: false,
  error: null,
  lastUpdated: 0
};

// Cart reducer
function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'SET_ITEMS': {
      const items = action.payload;
      const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...state,
        items,
        totalItems,
        totalPrice,
        lastUpdated: Date.now(),
        isLoading: false,
        error: null
      };
    }

    case 'LOAD_ITEMS': {
      const items = action.payload;
      const totalItems = items.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...state,
        items,
        totalItems,
        totalPrice,
        isLoading: false,
        error: null
        // Don't update lastUpdated when loading from storage
      };
    }
    
    case 'ADD_ITEM': {
      const existingItemIndex = state.items.findIndex(item => item.productId === action.payload.productId);
      let newItems: CartItem[];

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        newItems = state.items.map((item, index) =>
          index === existingItemIndex
            ? { ...item, quantity: item.quantity + action.payload.quantity }
            : item
        );
      } else {
        // Add new item
        newItems = [...state.items, action.payload];
      }

      const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...state,
        items: newItems,
        totalItems,
        totalPrice,
        lastUpdated: Date.now(),
        error: null
      };
    }
    
    case 'REMOVE_ITEM': {
      const newItems = state.items.filter(item => item.productId !== action.payload);
      const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...state,
        items: newItems,
        totalItems,
        totalPrice,
        lastUpdated: Date.now(),
        error: null
      };
    }

    case 'REMOVE_MULTIPLE_ITEMS': {
      const productIdsToRemove = action.payload;
      const newItems = state.items.filter(item => !productIdsToRemove.includes(item.productId));
      const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);

      return {
        ...state,
        items: newItems,
        totalItems,
        totalPrice,
        lastUpdated: Date.now(),
        error: null
      };
    }
    
    case 'UPDATE_QUANTITY': {
      const { productId, quantity } = action.payload;
      
      if (quantity <= 0) {
        // Remove item if quantity is 0 or negative
        const newItems = state.items.filter(item => item.productId !== productId);
        const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        return {
          ...state,
          items: newItems,
          totalItems,
          totalPrice,
          lastUpdated: Date.now(),
          error: null
        };
      }
      
      const newItems = state.items.map(item =>
        item.productId === productId
          ? { ...item, quantity }
          : item
      );
      
      const totalItems = newItems.reduce((sum, item) => sum + item.quantity, 0);
      const totalPrice = newItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
      
      return {
        ...state,
        items: newItems,
        totalItems,
        totalPrice,
        lastUpdated: Date.now(),
        error: null
      };
    }
    
    case 'CLEAR_CART':
      return {
        ...state,
        items: [],
        totalItems: 0,
        totalPrice: 0,
        lastUpdated: Date.now(),
        error: null
      };
    
    default:
      return state;
  }
}

// Create context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component
interface CartProviderProps {
  children: ReactNode;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(cartReducer, initialState);

  // Load cart from storage on mount
  useEffect(() => {
    const loadCartData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true });

      try {
        const savedItems = await CartStorageService.loadCart();
        dispatch({ type: 'LOAD_ITEMS', payload: savedItems });
      } catch (error) {
        console.error('Failed to load cart:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to load cart data' });
      }
    };

    loadCartData();
  }, []);

  // Save cart to storage whenever items change
  useEffect(() => {
    // Always save cart when items change, including when cart becomes empty
    // Skip the initial load to avoid overwriting with empty cart
    if (state.lastUpdated > 0) {
      CartStorageService.saveCart(state.items).catch(error => {
        console.error('Failed to save cart:', error);
        dispatch({ type: 'SET_ERROR', payload: 'Failed to save cart data' });
      });
    }
  }, [state.items, state.lastUpdated]);

  // Add item to cart
  const addItem = useCallback(async (productId: string, quantity: number = 1): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null });

      // Find the product
      const product = allProducts.find(p => p.id === productId);

      if (!product) {
        dispatch({ type: 'SET_ERROR', payload: 'Product not found' });
        return false;
      }

      // Check if product is in stock
      if (!product.inStock) {
        dispatch({ type: 'SET_ERROR', payload: 'Product is out of stock' });
        return false;
      }

      // Create cart item
      const cartItem: CartItem = {
        id: uuidv4(),
        productId: product.id,
        name: product.name,
        price: product.price,
        imageUrl: product.imageUrl,
        quantity,
        addedAt: Date.now(),
        category: product.category,
        description: product.description
      };

      dispatch({ type: 'ADD_ITEM', payload: cartItem });

      // Save cart state to memory for persistence
      try {
        const cartSummary = `Cart updated: Added ${product.name} (${quantity}x) - $${(product.price * quantity).toFixed(2)}`;
        localStorage.setItem('cart-memory', cartSummary);
      } catch (memoryError) {
        console.warn('Failed to save cart memory:', memoryError);
      }

      return true;
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to add item to cart' });
      return false;
    }
  }, []);

  // Remove item from cart
  const removeItem = useCallback(async (productId: string): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null });
      dispatch({ type: 'REMOVE_ITEM', payload: productId });
      return true;
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to remove item from cart' });
      return false;
    }
  }, []);

  // Remove multiple items from cart
  const removeMultipleItems = useCallback(async (productIds: string[]): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null });
      dispatch({ type: 'REMOVE_MULTIPLE_ITEMS', payload: productIds });
      return true;
    } catch (error) {
      console.error('Failed to remove multiple items from cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to remove multiple items from cart' });
      return false;
    }
  }, []);

  // Update item quantity
  const updateQuantity = useCallback(async (productId: string, quantity: number): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null });
      dispatch({ type: 'UPDATE_QUANTITY', payload: { productId, quantity } });
      return true;
    } catch (error) {
      console.error('Failed to update item quantity:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to update item quantity' });
      return false;
    }
  }, []);

  // Clear cart
  const clearCart = useCallback(async (): Promise<boolean> => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null });
      dispatch({ type: 'CLEAR_CART' });
      await CartStorageService.clearCart();
      return true;
    } catch (error) {
      console.error('Failed to clear cart:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to clear cart' });
      return false;
    }
  }, []);

  // Get cart item
  const getCartItem = useCallback((productId: string): CartItem | undefined => {
    return state.items.find(item => item.productId === productId);
  }, [state.items]);

  // Check if item is in cart
  const isInCart = useCallback((productId: string): boolean => {
    return state.items.some(item => item.productId === productId);
  }, [state.items]);

  const contextValue: CartContextType = {
    ...state,
    addItem,
    removeItem,
    removeMultipleItems,
    updateQuantity,
    clearCart,
    getCartItem,
    isInCart
  };

  return (
    <CartContext.Provider value={contextValue}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export default CartContext;
