import React, { useState } from 'react';
import { useCart } from '../../context/CartContext';
import type { CartItem as CartItemType } from '../../types/cart';
import './CartItem.css';

interface CartItemProps {
  item: CartItemType;
}

const CartItem: React.FC<CartItemProps> = ({ item }) => {
  const { updateQuantity, removeItem } = useCart();
  const [isUpdating, setIsUpdating] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleQuantityChange = async (newQuantity: number) => {
    if (newQuantity < 1) return;
    
    setIsUpdating(true);
    try {
      await updateQuantity(item.productId, newQuantity);
    } catch (error) {
      console.error('Failed to update quantity:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleRemove = async () => {
    setIsUpdating(true);
    try {
      await removeItem(item.productId);
    } catch (error) {
      console.error('Failed to remove item:', error);
      setIsUpdating(false);
    }
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const fallbackImageUrl = 'https://via.placeholder.com/80x80/f0f4ff/4a6cf7?text=Product';

  return (
    <div className={`cart-item ${isUpdating ? 'updating' : ''}`}>
      <div className="cart-item-image">
        <img
          src={imageError ? fallbackImageUrl : item.imageUrl}
          alt={item.name}
          onError={handleImageError}
        />
      </div>

      <div className="cart-item-details">
        <div className="cart-item-info">
          <h4 className="cart-item-name">{item.name}</h4>
          <p className="cart-item-category">{item.category}</p>
          <p className="cart-item-price">${item.price.toFixed(2)}</p>
        </div>

        <div className="cart-item-controls">
          <div className="quantity-controls">
            <button
              className="quantity-btn"
              onClick={() => handleQuantityChange(item.quantity - 1)}
              disabled={isUpdating || item.quantity <= 1}
              aria-label="Decrease quantity"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>

            <span className="quantity-display">
              {isUpdating ? (
                <div className="quantity-loading">
                  <div className="loading-spinner small"></div>
                </div>
              ) : (
                item.quantity
              )}
            </span>

            <button
              className="quantity-btn"
              onClick={() => handleQuantityChange(item.quantity + 1)}
              disabled={isUpdating}
              aria-label="Increase quantity"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 5V19M5 12H19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>

          <button
            className="remove-btn"
            onClick={handleRemove}
            disabled={isUpdating}
            aria-label="Remove item from cart"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3 6H5H21" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        </div>
      </div>

      <div className="cart-item-total">
        <span className="item-total-price">
          ${(item.price * item.quantity).toFixed(2)}
        </span>
      </div>

      {isUpdating && (
        <div className="cart-item-overlay">
          <div className="loading-spinner"></div>
        </div>
      )}
    </div>
  );
};

export default CartItem;
