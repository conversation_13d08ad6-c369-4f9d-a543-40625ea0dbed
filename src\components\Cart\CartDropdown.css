.cart-dropdown {
  position: absolute;
  top: calc(100% + var(--space-2));
  right: 0;
  width: 400px;
  max-height: 600px;
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  border: 1px solid var(--neutral-200);
  z-index: 9995; /* Above product overlay (9985) but below chat (9999) */
  overflow: hidden;
  animation: cartDropdownAppear 0.3s ease-out;
}

.cart-dropdown-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4) var(--space-5);
  border-bottom: 1px solid var(--neutral-200);
  background: var(--gradient-card);
}

.cart-dropdown-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--neutral-900);
}

.close-button {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neutral-500);
  background: var(--neutral-100);
  transition: all var(--transition-fast);
  border: none;
  cursor: pointer;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.close-button:hover {
  background: var(--neutral-200);
  color: var(--neutral-700);
  transform: scale(1.05);
}

.close-button:active {
  transform: scale(0.95);
}

.cart-dropdown-content {
  max-height: 500px;
  overflow-y: auto;
}

.cart-dropdown-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  gap: var(--space-3);
  color: var(--neutral-600);
}

.cart-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8);
  text-align: center;
  gap: var(--space-4);
}

.cart-empty-icon {
  color: var(--neutral-400);
  margin-bottom: var(--space-2);
}

.cart-empty h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--neutral-700);
}

.cart-empty p {
  margin: 0;
  color: var(--neutral-500);
  font-size: 0.9rem;
}

.cart-items {
  padding: var(--space-3);
  max-height: 300px;
  overflow-y: auto;
}

.cart-summary {
  border-top: 1px solid var(--neutral-200);
  padding: var(--space-4) var(--space-5);
  background: var(--neutral-50);
}

.cart-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  margin-bottom: var(--space-4);
}

.cart-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-stat-label {
  font-size: 0.9rem;
  color: var(--neutral-600);
}

.cart-stat-value {
  font-weight: 600;
  color: var(--neutral-900);
}

.cart-stat.cart-total {
  padding-top: var(--space-2);
  border-top: 1px solid var(--neutral-200);
  font-size: 1.1rem;
}

.cart-stat.cart-total .cart-stat-value {
  color: var(--primary-600);
  font-size: 1.2rem;
}

.cart-actions {
  display: flex;
  gap: var(--space-3);
}

.cart-clear-btn {
  flex: 1;
  padding: var(--space-3) var(--space-4);
  font-size: 0.9rem;
}

.cart-checkout-btn {
  flex: 2;
  padding: var(--space-3) var(--space-4);
  font-size: 0.9rem;
  font-weight: 600;
}

/* Custom scrollbar for cart items */
.cart-items::-webkit-scrollbar {
  width: 6px;
}

.cart-items::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-full);
}

.cart-items::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-full);
}

.cart-items::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

@keyframes cartDropdownAppear {
  0% {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .cart-dropdown {
    width: calc(100vw - var(--space-4));
    right: var(--space-2);
  }
  
  .cart-actions {
    flex-direction: column;
  }
  
  .cart-clear-btn,
  .cart-checkout-btn {
    flex: 1;
  }
}
