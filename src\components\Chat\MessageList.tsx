import { useEffect, useRef } from 'react';
import type { FC } from 'react';
import { useChat } from '../../context/ChatContext';
import MessageItem from './MessageItem';
import './MessageList.css';

const MessageList: FC = () => {
  const { state } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom();
  }, [state.messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const { config } = useChat();

  return (
    <div className="message-list">
      {state.messages.map((message) => (
        <MessageItem key={message.id} message={message} />
      ))}

      {state.isTyping && (
        <div className="typing-container">
          <div className="agent-avatar">
            {config.agentAvatar ? (
              <img src={config.agentAvatar} alt={config.agentName} />
            ) : (
              <div className="agent-avatar-placeholder">
                {config.agentName.charAt(0)}
              </div>
            )}
          </div>
          <div className="typing-indicator">
            <span className="typing-text">{config.agentName} is typing</span>
            <div className="typing-dots">
              <div className="typing-dot"></div>
              <div className="typing-dot"></div>
              <div className="typing-dot"></div>
            </div>
          </div>
        </div>
      )}

      <div ref={messagesEndRef} />
    </div>
  );
};

export default MessageList;
