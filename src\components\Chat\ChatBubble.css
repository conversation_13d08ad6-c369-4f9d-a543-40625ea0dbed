.chat-bubble {
  position: fixed;
  bottom: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--chat-bubble-bg);
  color: var(--chat-bubble-text);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: none;
  box-shadow: var(--chat-shadow);
  transition: transform var(--chat-transition-speed), box-shadow var(--chat-transition-speed);
  z-index: 9999;
}

.chat-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15);
}

.chat-bubble.has-notification {
  animation: pulse 2s infinite;
}

.chat-bubble-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-notification {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

@media (max-width: 768px) {
  .chat-bubble {
    width: 50px;
    height: 50px;
    bottom: 16px;
  }
  
  .chat-bubble-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .chat-notification {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
