/* ===== DESIGN SYSTEM ===== */
:root {
  /* Primary Colors */
  --primary-50: #f0f4ff;
  --primary-100: #e0e7ff;
  --primary-200: #c7d2fe;
  --primary-300: #a5b4fc;
  --primary-400: #818cf8;
  --primary-500: #6366f1;
  --primary-600: #4f46e5;
  --primary-700: #4338ca;
  --primary-800: #3730a3;
  --primary-900: #312e81;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic Colors */
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-hero: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-card: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Typography */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-display: 'Cal Sans', 'Inter', sans-serif;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* ===== RESET & BASE STYLES ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-sans);
  font-size: 1rem;
  line-height: 1.6;
  color: var(--neutral-800);
  background-color: var(--neutral-50);
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: inherit;
  transition: color var(--transition-fast);
}

ul, ol {
  list-style: none;
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

button {
  font-family: inherit;
  cursor: pointer;
  border: none;
  background: none;
  transition: all var(--transition-fast);
}

/* ===== LAYOUT ===== */
.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6);
}

@media (max-width: 640px) {
  .container {
    padding: 0 var(--space-4);
  }
}

/* ===== HEADER ===== */
.header {
  position: sticky;
  top: 0;
  z-index: 1000; /* Restored original z-index */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--neutral-200);
  transition: all var(--transition-normal);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.03), transparent);
  pointer-events: none;
}

.header .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80px;
  position: relative;
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.logo h1 {
  font-family: var(--font-display);
  font-size: 1.75rem;
  font-weight: 800;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.025em;
}

.logo::before {
  content: '';
  width: 32px;
  height: 32px;
  background: var(--gradient-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-md);
}

.nav ul {
  display: flex;
  align-items: center;
  gap: var(--space-8);
}

.nav a {
  font-weight: 500;
  font-size: 0.95rem;
  color: var(--neutral-700);
  padding: var(--space-2) var(--space-4);
  border-radius: var(--radius-full);
  position: relative;
  transition: all var(--transition-fast);
  z-index: 1;
}

.nav a::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-50);
  border-radius: var(--radius-full);
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--transition-fast);
  z-index: -1;
}

.nav a:hover::before,
.nav a.active::before {
  opacity: 1;
  transform: scale(1);
}

.nav a:hover,
.nav a.active {
  color: var(--primary-600);
  transform: translateY(-1px);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.icon-button {
  width: 44px;
  height: 44px;
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neutral-600);
  position: relative;
  background: var(--neutral-100);
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.icon-button:hover {
  background: var(--primary-50);
  color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.icon-button:active {
  transform: translateY(0);
}

.badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  height: 20px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--space-1);
  box-shadow: var(--shadow-md);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* ===== HERO SECTION ===== */
.hero {
  position: relative;
  background: var(--gradient-hero);
  color: white;
  padding: var(--space-24) 0;
  text-align: center;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  animation: float 20s ease-in-out infinite;
}

.hero::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: rotate 30s linear infinite;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.hero h2 {
  font-family: var(--font-display);
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--space-6);
  letter-spacing: -0.025em;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero h2 span {
  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero p {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: var(--space-8);
  opacity: 0.95;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.btn-primary {
  background: white;
  color: var(--primary-600);
  border: none;
  padding: var(--space-4) var(--space-8);
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-xl);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left var(--transition-slow);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-2xl);
  color: var(--primary-700);
}

.btn-primary:active {
  transform: translateY(-1px);
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== PRODUCT SECTIONS ===== */
.featured-products {
  padding: var(--space-24) 0;
  background: linear-gradient(180deg, var(--neutral-50) 0%, white 100%);
}

.section-title {
  text-align: center;
  font-family: var(--font-display);
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 700;
  margin-bottom: var(--space-16);
  color: var(--neutral-900);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -var(--space-4);
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-8);
  margin-top: var(--space-12);
}

.product-card {
  background: var(--gradient-card);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  transition: all var(--transition-normal);
  position: relative;
  border: 1px solid var(--neutral-200);
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
  opacity: 0;
  transition: opacity var(--transition-normal);
}

.product-card:hover::before {
  opacity: 1;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-200);
}

.product-image {
  height: 240px;
  background: linear-gradient(135deg, var(--primary-50) 0%, var(--neutral-100) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.product-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
}

.product-card:hover .product-image::after {
  transform: translateX(100%);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

.product-info {
  padding: var(--space-6);
}

.product-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: var(--space-3);
  color: var(--neutral-900);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-600);
  margin-bottom: var(--space-3);
}

.product-description {
  font-size: 0.9rem;
  color: var(--neutral-600);
  margin-bottom: var(--space-4);
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-5);
  font-size: 0.9rem;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  color: var(--warning);
  font-weight: 600;
}

.product-rating::before {
  content: '★';
  color: var(--warning);
}

.product-stock {
  font-weight: 500;
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.8rem;
}

.product-stock.in-stock {
  color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.product-stock.out-of-stock {
  color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}

/* ===== CATEGORY FILTER ===== */
.category-filter {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: var(--space-3);
  margin-bottom: var(--space-12);
  padding: var(--space-6);
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
}

.category-btn {
  background: var(--neutral-100);
  color: var(--neutral-700);
  border: 1px solid var(--neutral-200);
  padding: var(--space-3) var(--space-5);
  border-radius: var(--radius-full);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--gradient-primary);
  transition: left var(--transition-normal);
  z-index: -1;
}

.category-btn:hover::before,
.category-btn.active::before {
  left: 0;
}

.category-btn:hover,
.category-btn.active {
  color: white;
  border-color: var(--primary-600);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* ===== BUTTONS ===== */
.load-more {
  text-align: center;
  margin-top: var(--space-16);
}

.btn-secondary {
  background: transparent;
  color: var(--primary-600);
  border: 2px solid var(--primary-600);
  padding: var(--space-3) var(--space-6);
  font-size: 1rem;
  font-weight: 600;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 300px;
}

.btn-secondary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: var(--primary-600);
  transition: left var(--transition-normal);
  z-index: -1;
}

.btn-secondary:hover::before {
  left: 0;
}

.btn-secondary:hover {
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary.in-cart {
  background: var(--success);
  border-color: var(--success);
  color: white;
}

.btn-secondary.in-cart:hover {
  background: #059669;
  border-color: #059669;
}

.btn-secondary.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--neutral-200);
  border-color: var(--neutral-300);
  color: var(--neutral-500);
}

.btn-secondary.disabled:hover {
  transform: none;
  box-shadow: none;
}

.button-loading {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.button-in-cart {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

/* ===== FOOTER ===== */
.footer {
  background: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 100%);
  color: white;
  padding: var(--space-24) 0 var(--space-8);
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Cpath d='M20 20c0-11.046-8.954-20-20-20v20h20z'/%3E%3C/g%3E%3C/svg%3E");
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--space-16);
  margin-bottom: var(--space-16);
  position: relative;
  z-index: 2;
}

.footer-logo {
  max-width: 400px;
}

.footer-logo h2 {
  font-family: var(--font-display);
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: var(--space-4);
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-logo p {
  opacity: 0.8;
  line-height: 1.6;
  font-size: 1.1rem;
}

.footer-links {
  display: contents;
}

.footer-column h3 {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--space-6);
  color: var(--primary-300);
  position: relative;
}

.footer-column h3::after {
  content: '';
  position: absolute;
  bottom: -var(--space-2);
  left: 0;
  width: 30px;
  height: 2px;
  background: var(--primary-500);
  border-radius: var(--radius-full);
}

.footer-column ul li {
  margin-bottom: var(--space-3);
}

.footer-column a {
  opacity: 0.8;
  transition: all var(--transition-fast);
  font-size: 0.95rem;
  position: relative;
}

.footer-column a::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--primary-400);
  transition: width var(--transition-fast);
}

.footer-column a:hover::before {
  width: 100%;
}

.footer-column a:hover {
  opacity: 1;
  color: var(--primary-300);
  transform: translateX(4px);
}

.footer-bottom {
  text-align: center;
  padding-top: var(--space-8);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  opacity: 0.6;
  font-size: 0.9rem;
  position: relative;
  z-index: 2;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--space-6);
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-12);
  }
}

@media (max-width: 768px) {
  .header .container {
    height: 70px;
  }

  .nav ul {
    gap: var(--space-4);
  }

  .nav a {
    font-size: 0.85rem;
    padding: var(--space-1) var(--space-3);
  }

  .hero {
    padding: var(--space-20) 0;
  }

  .hero h2 {
    font-size: clamp(2rem, 8vw, 3rem);
  }

  .hero p {
    font-size: 1.1rem;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--space-5);
  }

  .category-filter {
    padding: var(--space-4);
    gap: var(--space-2);
  }

  .category-btn {
    padding: var(--space-2) var(--space-4);
    font-size: 0.85rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: var(--space-10);
    text-align: center;
  }

  .footer-column h3::after {
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-3);
  }

  .header .container {
    height: 60px;
  }

  .logo h1 {
    font-size: 1.5rem;
  }

  .nav {
    display: none;
  }

  .hero {
    padding: var(--space-16) 0;
  }

  .product-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .product-card {
    margin: 0 var(--space-2);
  }

  .category-filter {
    margin: 0 var(--space-2) var(--space-8);
  }

  .btn-secondary {
    width: calc(100% - var(--space-4));
    margin: 0 var(--space-2);
  }
}

/* ===== UTILITY CLASSES ===== */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

/* ===== LOADING STATES ===== */
.loading-skeleton {
  background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--neutral-200);
  border-top: 2px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== FOCUS STATES ===== */
*:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* ===== SCROLL BEHAVIOR ===== */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* ===== PERFORMANCE OPTIMIZATIONS ===== */
.product-card,
.category-btn,
.btn-primary,
.btn-secondary,
.icon-button {
  will-change: transform;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
