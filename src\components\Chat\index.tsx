import { useEffect } from 'react';
import type { FC } from 'react';
import { ChatProvider, useChat } from '../../context/ChatContext';
import type { ChatConfig } from '../../types/chat';
import ChatBubble from './ChatBubble';
import ChatWindow from './ChatWindow';
import '../../styles/chat.css';
import './Chat.css';

// Internal component that uses the chat context
const ChatComponent: FC = () => {
  const { config, toggleChat } = useChat();

  // Initialize chat based on config
  useEffect(() => {
    if (config.theme.initialState === 'expanded') {
      toggleChat();
    }
  }, [config.theme.initialState, toggleChat]);

  return (
    <div className="chat-container">
      <ChatBubble />
      <ChatWindow />
    </div>
  );
};

// Main component that wraps the chat with the provider
interface ChatProps {
  config?: Partial<ChatConfig>;
}

const Chat: FC<ChatProps> = ({ config }) => {
  return (
    <ChatProvider config={config}>
      <ChatComponent />
    </ChatProvider>
  );
};

export default Chat;
