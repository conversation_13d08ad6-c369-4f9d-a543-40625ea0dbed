import { BookOpen, Users, Award, Calendar } from 'lucide-react';

const Academics = () => {
  return (
    <div className="academics-page">
      {/* Page Header */}
      <section className="page-header">
        <div className="container">
          <h1>აკადემიური პროგრამები / Academic Programs</h1>
          <p>მრავალფეროვანი საგანმანათლებლო შესაძლებლობები</p>
          <p>Diverse educational opportunities</p>
        </div>
      </section>

      {/* Faculties */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2>ფაკულტეტები / Faculties</h2>
            <p>ჩვენი უნივერსიტეტის აკადემიური ერთეულები</p>
            <p>Academic units of our university</p>
          </div>
          <div className="faculties-grid grid grid-2">
            <div className="faculty-card card">
              <BookOpen size={40} />
              <h3>ბიზნეს ფაკულტეტი</h3>
              <h4>Business Faculty</h4>
              <ul>
                <li>ბიზნეს ადმინისტრირება / Business Administration</li>
                <li>ეკონომიკა / Economics</li>
                <li>ფინანსები / Finance</li>
                <li>მარკეტინგი / Marketing</li>
              </ul>
            </div>
            <div className="faculty-card card">
              <BookOpen size={40} />
              <h3>ტექნოლოგიების ფაკულტეტი</h3>
              <h4>Technology Faculty</h4>
              <ul>
                <li>ინფორმაციული ტექნოლოგიები / Information Technology</li>
                <li>კომპიუტერული მეცნიერება / Computer Science</li>
                <li>ინჟინერია / Engineering</li>
                <li>მათემატიკა / Mathematics</li>
              </ul>
            </div>
            <div className="faculty-card card">
              <BookOpen size={40} />
              <h3>იურიდიული ფაკულტეტი</h3>
              <h4>Law Faculty</h4>
              <ul>
                <li>იურისპრუდენცია / Law</li>
                <li>საერთაშორისო სამართალი / International Law</li>
                <li>კრიმინალისტიკა / Criminology</li>
                <li>ადმინისტრაციული სამართალი / Administrative Law</li>
              </ul>
            </div>
            <div className="faculty-card card">
              <BookOpen size={40} />
              <h3>ჰუმანიტარული ფაკულტეტი</h3>
              <h4>Humanities Faculty</h4>
              <ul>
                <li>ფილოლოგია / Philology</li>
                <li>ისტორია / History</li>
                <li>ფსიქოლოგია / Psychology</li>
                <li>ჟურნალისტიკა / Journalism</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Degree Programs */}
      <section className="section section-alt">
        <div className="container">
          <div className="section-header">
            <h2>საგანმანათლებლო დონეები / Degree Levels</h2>
            <p>სხვადასხვა დონის აკადემიური პროგრამები</p>
            <p>Academic programs at different levels</p>
          </div>
          <div className="degrees-grid grid grid-3">
            <div className="degree-card card">
              <Award size={40} />
              <h3>ბაკალავრიატი</h3>
              <h4>Bachelor's Degree</h4>
              <p><strong>ხანგრძლივობა:</strong> 4 წელი</p>
              <p><strong>Duration:</strong> 4 years</p>
              <p><strong>კრედიტები:</strong> 240 ECTS</p>
              <p><strong>Credits:</strong> 240 ECTS</p>
              <p>ძირითადი უნივერსიტეტული განათლება</p>
              <p>Fundamental university education</p>
            </div>
            <div className="degree-card card">
              <Award size={40} />
              <h3>მაგისტრატურა</h3>
              <h4>Master's Degree</h4>
              <p><strong>ხანგრძლივობა:</strong> 2 წელი</p>
              <p><strong>Duration:</strong> 2 years</p>
              <p><strong>კრედიტები:</strong> 120 ECTS</p>
              <p><strong>Credits:</strong> 120 ECTS</p>
              <p>სპეციალიზებული მაღალი განათლება</p>
              <p>Specialized higher education</p>
            </div>
            <div className="degree-card card">
              <Award size={40} />
              <h3>დოქტორანტურა</h3>
              <h4>Doctoral Degree</h4>
              <p><strong>ხანგრძლივობა:</strong> 3-4 წელი</p>
              <p><strong>Duration:</strong> 3-4 years</p>
              <p><strong>კრედიტები:</strong> 180 ECTS</p>
              <p><strong>Credits:</strong> 180 ECTS</p>
              <p>კვლევითი და სამეცნიერო მუშაობა</p>
              <p>Research and scientific work</p>
            </div>
          </div>
        </div>
      </section>

      {/* Academic Calendar */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2>აკადემიური კალენდარი / Academic Calendar</h2>
            <p>მნიშვნელოვანი თარიღები და ღონისძიებები</p>
            <p>Important dates and events</p>
          </div>
          <div className="calendar-grid grid grid-2">
            <div className="semester-card card">
              <Calendar size={30} />
              <h3>შემოდგომის სემესტრი</h3>
              <h4>Fall Semester</h4>
              <ul>
                <li><strong>დაწყება:</strong> სექტემბერი 15 / <strong>Start:</strong> September 15</li>
                <li><strong>დასრულება:</strong> იანვარი 31 / <strong>End:</strong> January 31</li>
                <li><strong>გამოცდები:</strong> იანვარი / <strong>Exams:</strong> January</li>
                <li><strong>რეგისტრაცია:</strong> ივლისი-აგვისტო / <strong>Registration:</strong> July-August</li>
              </ul>
            </div>
            <div className="semester-card card">
              <Calendar size={30} />
              <h3>გაზაფხულის სემესტრი</h3>
              <h4>Spring Semester</h4>
              <ul>
                <li><strong>დაწყება:</strong> თებერვალი 15 / <strong>Start:</strong> February 15</li>
                <li><strong>დასრულება:</strong> ივნისი 30 / <strong>End:</strong> June 30</li>
                <li><strong>გამოცდები:</strong> ივნისი / <strong>Exams:</strong> June</li>
                <li><strong>რეგისტრაცია:</strong> იანვარი / <strong>Registration:</strong> January</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Academic Requirements */}
      <section className="section section-alt">
        <div className="container">
          <div className="section-header">
            <h2>აკადემიური მოთხოვნები / Academic Requirements</h2>
            <p>წარმატებული სწავლისთვის საჭირო პირობები</p>
            <p>Conditions required for successful studies</p>
          </div>
          <div className="requirements-grid grid grid-2">
            <div className="requirement-card card">
              <Users size={30} />
              <h3>დასწრება / Attendance</h3>
              <p>სტუდენტებმა უნდა დაესწრონ ლექციების მინიმუმ 75%-ს</p>
              <p>Students must attend at least 75% of lectures</p>
            </div>
            <div className="requirement-card card">
              <Award size={30} />
              <h3>შეფასება / Assessment</h3>
              <p>მინიმალური ქულა გასავლელად არის 51 ქულა (100-დან)</p>
              <p>Minimum passing grade is 51 points (out of 100)</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Academics;
