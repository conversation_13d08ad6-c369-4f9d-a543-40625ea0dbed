# Premium Store with CHASTER

An e-commerce store with an AI-powered chat assistant that helps customers find products.

## Features

- Interactive product catalog with categories
- AI-powered chat assistant (CHASTER) using Groq's LLama 3.3 70B model
- Product recommendation system
- Dark theme UI
- Responsive design

## Deployment on GitLab Pages

This project is configured to be automatically deployed to GitLab Pages when changes are pushed to the main branch.

### Accessing the Deployed Site

Once deployed, the site will be available at:
`https://[your-gitlab-username].gitlab.io/e-commerce-chat/`

### Local Development

To run the project locally:

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Configuration

If you fork this repository, you may need to update the `base` path in `vite.config.ts` to match your repository name:

```typescript
export default defineConfig({
  plugins: [react()],
  base: '/your-repo-name/',
})
```

## Technologies Used

- React
- TypeScript
- Vite
- Groq API (LLama 3.3 70B model)
