.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-top-left-radius: var(--chat-border-radius);
  border-top-right-radius: var(--chat-border-radius);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.chat-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
}

.chat-header-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-header-avatar-placeholder {
  font-size: 18px;
  font-weight: 600;
  color: var(--chat-header-text);
}

.chat-header-title {
  display: flex;
  flex-direction: column;
}

.chat-header-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chat-header-status {
  font-size: 12px;
  opacity: 0.8;
}

/* Header actions container */
.chat-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Common styles for header buttons */
.chat-header-close,
.chat-header-clear {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s, transform 0.2s;
}

.chat-header-close:hover,
.chat-header-clear:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

/* Confirmation dialog */
.chat-header-confirm {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 16px;
  animation: fadeIn 0.2s ease-out;
}

.chat-header-confirm span {
  font-size: 12px;
  white-space: nowrap;
}

.chat-header-confirm-yes,
.chat-header-confirm-no {
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  transition: background-color 0.2s;
}

.chat-header-confirm-yes {
  background-color: rgba(255, 255, 255, 0.2);
}

.chat-header-confirm-yes:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.chat-header-confirm-no:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .chat-header {
    padding: 12px;
  }

  .chat-header-avatar {
    width: 32px;
    height: 32px;
  }

  .chat-header-title h3 {
    font-size: 14px;
  }

  .chat-header-status {
    font-size: 10px;
  }

  .chat-header-confirm {
    padding: 2px 6px;
  }

  .chat-header-confirm span {
    font-size: 10px;
  }

  .chat-header-confirm-yes,
  .chat-header-confirm-no {
    padding: 2px 6px;
    font-size: 10px;
  }
}
