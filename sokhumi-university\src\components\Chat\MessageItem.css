/* University Message Item Styles */
.message-item {
  display: flex;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-out;
  max-width: 85%;
  align-items: flex-end;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-agent {
  margin-right: auto;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid rgba(245, 158, 11, 0.3);
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
}

.message-avatar-placeholder.user-avatar {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 16px;
  position: relative;
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1);
  display: inline-block;
  min-width: 60px;
  width: auto;
  max-width: 300px;
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.message-user .message-bubble {
  border-top-right-radius: 4px;
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%) !important;
  color: white !important;
}

.message-agent .message-bubble {
  border-top-left-radius: 4px;
  background-color: white !important;
  color: #1f2937 !important;
}

.message-content {
  word-break: break-word;
  white-space: pre-line;
  line-height: 1.5;
  max-width: 100%;
  overflow-wrap: break-word;
  hyphens: auto;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.message-content.single-line {
  white-space: pre-line;
  overflow: visible;
  max-width: 100%;
}

/* Image styling */
.message-image-container {
  margin-bottom: 8px;
  max-width: 100%;
}

.message-image {
  max-width: 240px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
  display: block;
}

.message-image:hover {
  transform: scale(1.02);
}

.message-image-name {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 240px;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 4px;
  font-size: 10px;
  opacity: 0.7;
}

.message-time {
  margin-right: 4px;
  font-family: 'Inter', sans-serif;
}

.message-status-icon {
  margin-left: 4px;
}

.message-status-read {
  color: #f59e0b;
}

@media (max-width: 768px) {
  .message-item {
    max-width: 90%;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    margin: 0 4px;
  }

  .message-bubble {
    padding: 8px 12px;
    max-width: 240px;
    border-radius: 12px;
  }

  .message-user .message-bubble {
    border-top-right-radius: 3px;
  }

  .message-agent .message-bubble {
    border-top-left-radius: 3px;
  }

  .message-content {
    font-size: 14px;
  }

  .message-content.single-line {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .message-bubble {
    max-width: 200px;
    border-radius: 10px;
  }
}
