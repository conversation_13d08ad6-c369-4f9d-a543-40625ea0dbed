.message-item {
  display: flex;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-out;
  max-width: 85%;
  align-items: flex-end;
}

.message-user {
  margin-left: auto;
  flex-direction: row-reverse;
}

.message-agent {
  margin-right: auto;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 8px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.message-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--chat-primary-color);
  color: white;
  font-weight: 600;
  font-size: 14px;
}

.message-avatar-placeholder.user-avatar {
  background-color: #4a4a5a;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: var(--chat-message-border-radius);
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  display: inline-block;
  min-width: 60px;
  width: auto;
  max-width: 300px;
}

.message-user .message-bubble {
  border-top-right-radius: 4px;
}

.message-agent .message-bubble {
  border-top-left-radius: 4px;
}

.message-content {
  word-break: break-word;
  white-space: pre-line;
  line-height: 1.4;
  max-width: 100%;
  overflow-wrap: break-word;
  hyphens: auto;
}

/* Special styling for single-line messages - removed truncation */
.message-content.single-line {
  white-space: pre-line; /* Changed from nowrap to pre-line to allow wrapping */
  overflow: visible; /* Changed from hidden to visible */
  max-width: 100%; /* Increased from 300px to 100% */
}

/* Image styling */
.message-image-container {
  margin-bottom: 8px;
  max-width: 100%;
}

.message-image {
  max-width: 240px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.2s;
  display: block;
}

.message-image:hover {
  transform: scale(1.02);
}

.message-image-name {
  font-size: 12px;
  margin-top: 4px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 240px;
}

.message-meta {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-top: 4px;
  font-size: 10px;
  opacity: 0.7;
}

.message-time {
  margin-right: 4px;
}

.message-status-icon {
  margin-left: 4px;
}

.message-status-read {
  color: #6dff8e;
}

@media (max-width: 768px) {
  .message-item {
    max-width: 90%;
  }

  .message-avatar {
    width: 28px;
    height: 28px;
    margin: 0 4px;
  }

  .message-bubble {
    padding: 8px 12px;
    max-width: 240px;
  }

  .message-content {
    font-size: 14px;
  }

  .message-content.single-line {
    max-width: 100%; /* Changed from 220px to 100% for consistency */
  }
}
