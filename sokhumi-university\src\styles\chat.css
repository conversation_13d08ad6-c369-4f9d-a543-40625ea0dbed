/* Chat Component Styles */

/* Chat Bubble */
.chat-bubble {
  position: fixed;
  bottom: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  color: white;
  transition: all 0.3s ease;
  font-size: 0;
}

.chat-bubble:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.chat-bubble.has-notification {
  animation: pulse 2s infinite;
}

.chat-bubble-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-notification {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  border: 2px solid white;
}

/* Chat Window */
.chat-window {
  position: fixed;
  bottom: 90px;
  width: 380px;
  height: 600px;
  background: #1e1e2d;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  z-index: 999;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Chat Header */
.chat-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #2a2a3e;
}

.chat-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7c5cff, #9d7bff);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.chat-header-text h3 {
  margin: 0;
  color: white;
  font-size: 16px;
  font-weight: 600;
}

.chat-header-text p {
  margin: 0;
  color: #a0a0b8;
  font-size: 12px;
}

.chat-close-btn {
  background: none;
  border: none;
  color: #a0a0b8;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 18px;
}

.chat-close-btn:hover {
  background: #2a2a3e;
  color: white;
}

/* Message List */
.message-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-list::-webkit-scrollbar {
  width: 4px;
}

.message-list::-webkit-scrollbar-track {
  background: transparent;
}

.message-list::-webkit-scrollbar-thumb {
  background: #3a3a4e;
  border-radius: 2px;
}

/* Message Item */
.message-item {
  display: flex;
  gap: 12px;
  animation: fadeIn 0.3s ease-out;
}

.message-item.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: bold;
  flex-shrink: 0;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-bubble {
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  line-height: 1.4;
  font-size: 14px;
}

.message-item.user .message-bubble {
  background: #7c5cff;
  color: white;
  border-bottom-right-radius: 6px;
}

.message-item.agent .message-bubble {
  background: #2a2a3e;
  color: #e0e0e8;
  border-bottom-left-radius: 6px;
}

.message-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: #6b6b7d;
  padding: 0 4px;
}

.message-item.user .message-meta {
  justify-content: flex-end;
}

.message-status {
  display: flex;
  align-items: center;
  gap: 2px;
}

/* Chat Input */
.chat-input-container {
  padding: 20px;
  border-top: 1px solid #2a2a3e;
}

.chat-input-wrapper {
  position: relative;
  display: flex;
  align-items: flex-end;
  gap: 12px;
  background: #2a2a3e;
  border-radius: 12px;
  padding: 8px;
}

.chat-input {
  flex: 1;
  background: transparent;
  border: none;
  outline: none;
  color: white;
  font-size: 14px;
  line-height: 1.4;
  padding: 8px 12px;
  resize: none;
  max-height: 120px;
  min-height: 20px;
}

.chat-input::placeholder {
  color: #6b6b7d;
}

.chat-input-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chat-input-btn {
  background: none;
  border: none;
  color: #a0a0b8;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-input-btn:hover {
  background: #3a3a4e;
  color: white;
}

.chat-input-btn.send {
  background: #7c5cff;
  color: white;
}

.chat-input-btn.send:hover {
  background: #6b4cff;
}

.chat-input-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #6b6b7d;
  animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 4px 12px rgba(124, 92, 255, 0.4);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
}

/* Focus styles */
.chat-focus-visible:focus-visible {
  outline: 2px solid #7c5cff;
  outline-offset: 2px;
}

/* Responsive */
@media (max-width: 480px) {
  .chat-window {
    width: calc(100vw - 40px);
    height: calc(100vh - 140px);
    bottom: 90px;
    left: 20px;
    right: 20px;
  }
  
  .chat-bubble {
    bottom: 20px;
    right: 20px;
  }
}
