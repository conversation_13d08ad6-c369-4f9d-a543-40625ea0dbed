/**
 * Simple in-memory cache service for university knowledge base
 * Improves performance by caching frequently accessed data
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // Time to live in milliseconds
}

class CacheService {
  private cache: Map<string, CacheEntry<any>>;
  private defaultTTL: number;

  constructor(defaultTTL: number = 5 * 60 * 1000) { // 5 minutes default
    this.cache = new Map();
    this.defaultTTL = defaultTTL;
    
    // Clean up expired entries every minute
    setInterval(() => this.cleanup(), 60 * 1000);
    
    console.log('🗄️ Cache service initialized with TTL:', defaultTTL / 1000, 'seconds');
  }

  /**
   * Get data from cache
   */
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }
    
    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      console.log('🗑️ Cache entry expired and removed:', key);
      return null;
    }
    
    console.log('✅ Cache hit:', key);
    return entry.data;
  }

  /**
   * Set data in cache
   */
  set<T>(key: string, data: T, ttl?: number): void {
    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL
    };
    
    this.cache.set(key, entry);
    console.log('💾 Cache set:', key, 'TTL:', (ttl || this.defaultTTL) / 1000, 'seconds');
  }

  /**
   * Check if key exists in cache and is not expired
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return false;
    }
    
    // Check if entry has expired
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  /**
   * Delete specific key from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      console.log('🗑️ Cache entry deleted:', key);
    }
    return deleted;
  }

  /**
   * Clear all cache entries
   */
  clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    console.log('🧹 Cache cleared, removed', size, 'entries');
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    keys: string[];
    memoryUsage: string;
  } {
    const keys = Array.from(this.cache.keys());
    
    // Rough memory usage calculation
    const memoryUsage = JSON.stringify(Array.from(this.cache.values())).length;
    
    return {
      size: this.cache.size,
      keys,
      memoryUsage: `${(memoryUsage / 1024).toFixed(2)} KB`
    };
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      console.log('🧹 Cache cleanup: removed', removedCount, 'expired entries');
    }
  }

  /**
   * Generate cache key for search queries
   */
  static generateSearchKey(query: string, category?: string): string {
    const normalizedQuery = query.toLowerCase().trim();
    const categoryPart = category ? `_${category}` : '';
    return `search_${normalizedQuery}${categoryPart}`;
  }

  /**
   * Generate cache key for category data
   */
  static generateCategoryKey(category: string): string {
    return `category_${category}`;
  }

  /**
   * Generate cache key for quick facts
   */
  static generateQuickFactsKey(): string {
    return 'quick_facts';
  }
}

// Export singleton instance
export const cacheService = new CacheService();
export default cacheService;
