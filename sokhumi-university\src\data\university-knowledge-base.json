{"metadata": {"lastUpdated": "2024-12-03", "version": "1.0.0", "university": "Sokhumi State University", "universityGeorgian": "სოხუმის სახელმწიფო უნივერსიტეტი", "description": "Comprehensive knowledge base for Sokhumi State University"}, "academicPrograms": {"undergraduate": {"computerScience": {"name": "Computer Science", "nameGeorgian": "კომპიუტერული მეცნიერებები", "degree": "Bachelor of Science", "duration": "4 years", "credits": 240, "language": "Georgian, English", "description": "Comprehensive program covering software development, algorithms, data structures, and modern computing technologies.", "descriptionGeorgian": "ყოვლისმომცველი პროგრამა, რომელიც მოიცავს პროგრამული უზრუნველყოფის შემუშავებას, ალგორითმებს, მონაცემთა სტრუქტურებს და თანამედროვე კომპიუტერულ ტექნოლოგიებს.", "courses": ["Programming Fundamentals", "Data Structures and Algorithms", "Database Systems", "Web Development", "Software Engineering", "Computer Networks", "Artificial Intelligence", "Machine Learning"], "admissionRequirements": {"minimumGPA": 3.0, "requiredSubjects": ["Mathematics", "Physics", "Georgian Language"], "entranceExam": true, "englishProficiency": "Intermediate"}, "careerOpportunities": ["Software Developer", "Data Scientist", "System Administrator", "Web Developer", "AI Engineer"], "tuitionFee": {"annual": 2500, "currency": "GEL", "scholarships": true}}, "businessAdministration": {"name": "Business Administration", "nameGeorgian": "ბიზნეს ადმინისტრირება", "degree": "Bachelor of Business Administration", "duration": "4 years", "credits": 240, "language": "Georgian, English", "description": "Comprehensive business education covering management, finance, marketing, and entrepreneurship.", "descriptionGeorgian": "ყოვლისმომცველი ბიზნეს განათლება, რომელიც მოიცავს მენეჯმენტს, ფინანსებს, მარკეტინგს და მეწარმეობას.", "courses": ["Principles of Management", "Financial Accounting", "Marketing Fundamentals", "Business Statistics", "Organizational Behavior", "Strategic Management", "International Business", "Entrepreneurship"], "admissionRequirements": {"minimumGPA": 2.8, "requiredSubjects": ["Mathematics", "Georgian Language", "History"], "entranceExam": true, "englishProficiency": "Intermediate"}, "careerOpportunities": ["Business Manager", "Financial Analyst", "Marketing Specialist", "Project Manager", "Entrepreneur"], "tuitionFee": {"annual": 2200, "currency": "GEL", "scholarships": true}}, "psychology": {"name": "Psychology", "nameGeorgian": "ფსიქოლოგია", "degree": "Bachelor of Arts in Psychology", "duration": "4 years", "credits": 240, "language": "Georgian", "description": "Study of human behavior, mental processes, and psychological research methods.", "descriptionGeorgian": "ადამიანის ქცევის, გონებრივი პროცესების და ფსიქოლოგიური კვლევის მეთოდების შესწავლა.", "courses": ["General Psychology", "Developmental Psychology", "Social Psychology", "Cognitive Psychology", "Research Methods", "Statistics in Psychology", "Abnormal Psychology", "Counseling Psychology"], "admissionRequirements": {"minimumGPA": 2.7, "requiredSubjects": ["Georgian Language", "Biology", "Mathematics"], "entranceExam": true, "englishProficiency": "Basic"}, "careerOpportunities": ["Clinical Psychologist", "School Counselor", "Research Assistant", "HR Specialist", "Social Worker"], "tuitionFee": {"annual": 2000, "currency": "GEL", "scholarships": true}}}, "graduate": {"masterInEducation": {"name": "Master in Education", "nameGeorgian": "განათლების მაგისტრი", "degree": "Master of Education", "duration": "2 years", "credits": 120, "language": "Georgian", "description": "Advanced program for educators focusing on modern teaching methodologies and educational leadership.", "descriptionGeorgian": "განვითარებული პროგრამა პედაგოგებისთვის, რომელიც ფოკუსირებულია თანამედროვე სწავლების მეთოდოლოგიებსა და განათლების ლიდერობაზე.", "courses": ["Educational Leadership", "Curriculum Development", "Educational Psychology", "Research Methods in Education", "Technology in Education", "Assessment and Evaluation"], "admissionRequirements": {"bachelorDegree": true, "minimumGPA": 3.2, "workExperience": "2 years in education", "entranceExam": true}, "tuitionFee": {"annual": 3000, "currency": "GEL", "scholarships": true}}}}, "faculty": {"computerScience": {"departmentHead": {"name": "Dr. <PERSON><PERSON><PERSON>", "nameGeorgian": "დოქტორი გიორგი მაისურაძე", "title": "Professor and Department Head", "email": "g.<PERSON>@sou.edu.ge", "phone": "+995 443 123401", "office": "Building A, Room 301", "specialization": "Artificial Intelligence, Machine Learning", "education": "PhD in Computer Science, Georgian Technical University", "experience": "15 years", "courses": ["Artificial Intelligence", "Machine Learning", "Advanced Algorithms"]}, "faculty": [{"name": "Dr. <PERSON><PERSON>", "nameGeorgian": "დოქტორი ნინო ხვედელიძე", "title": "Associate Professor", "email": "n.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>@sou.edu.ge", "specialization": "Software Engineering, Web Development", "courses": ["Software Engineering", "Web Development", "Database Systems"]}, {"name": "Prof. <PERSON><PERSON>", "nameGeorgian": "პროფესორი ლევან ცინცაძე", "title": "Professor", "email": "<PERSON>.<PERSON><PERSON><PERSON><PERSON><PERSON>@sou.edu.ge", "specialization": "Computer Networks, Cybersecurity", "courses": ["Computer Networks", "Network Security", "System Administration"]}]}, "business": {"departmentHead": {"name": "Dr. <PERSON>", "nameGeorgian": "დოქტორი თამარ გელაშვილი", "title": "Professor and Department Head", "email": "t.<PERSON>@sou.edu.ge", "phone": "+995 443 123402", "office": "Building B, Room 201", "specialization": "Strategic Management, International Business", "education": "PhD in Business Administration, Tbilisi State University", "experience": "12 years", "courses": ["Strategic Management", "International Business", "Business Ethics"]}}}, "admissions": {"applicationPeriods": {"fall2024": {"applicationStart": "2024-06-01", "applicationDeadline": "2024-07-15", "entranceExamDate": "2024-07-20", "resultsAnnouncement": "2024-08-01", "enrollmentDeadline": "2024-08-15"}, "spring2025": {"applicationStart": "2024-12-01", "applicationDeadline": "2025-01-15", "entranceExamDate": "2025-01-20", "resultsAnnouncement": "2025-02-01", "enrollmentDeadline": "2025-02-15"}}, "requirements": {"undergraduate": {"documents": ["High school diploma", "Academic transcripts", "National exam results", "ID copy", "Medical certificate", "Photos (3x4 cm)"], "fees": {"applicationFee": 50, "entranceExamFee": 30, "currency": "GEL"}}, "graduate": {"documents": ["Bachelor's degree diploma", "Academic transcripts", "Recommendation letters (2)", "Statement of purpose", "CV/Resume", "ID copy"], "fees": {"applicationFee": 75, "entranceExamFee": 50, "currency": "GEL"}}}, "scholarships": {"academic": {"name": "Academic Excellence Scholarship", "nameGeorgian": "აკადემიური ღირსების სტიპენდია", "coverage": "50-100% tuition", "requirements": "GPA 3.8+, entrance exam score 90%+", "renewable": true}, "needBased": {"name": "Financial Need Scholarship", "nameGeorgian": "ფინანსური საჭიროების სტიპენდია", "coverage": "25-75% tuition", "requirements": "Demonstrated financial need, GPA 3.0+", "renewable": true}}}, "research": {"centers": {"aiResearch": {"name": "Artificial Intelligence Research Center", "nameGeorgian": "ხელოვნური ინტელექტის კვლევითი ცენტრი", "director": "Dr. <PERSON><PERSON><PERSON>", "established": "2020", "focus": "Machine Learning, Natural Language Processing, Computer Vision", "projects": ["Georgian Language Processing", "Medical Image Analysis", "Smart City Solutions"], "funding": "Government grants, EU Horizon 2020", "publications": 25}, "businessInnovation": {"name": "Business Innovation Lab", "nameGeorgian": "ბიზნეს ინოვაციების ლაბორატორია", "director": "Dr. <PERSON>", "established": "2019", "focus": "Entrepreneurship, Digital Transformation, Sustainable Business", "projects": ["Digital Marketing for SMEs", "Sustainable Tourism Development", "Fintech Innovation"], "partnerships": ["Local businesses", "Chamber of Commerce", "EU programs"]}}, "opportunities": {"undergraduate": ["Research assistant positions", "Summer research programs", "Thesis projects", "Conference presentations"], "graduate": ["Research fellowships", "PhD preparation programs", "International collaborations", "Publication opportunities"]}}, "studentServices": {"academicSupport": {"tutoring": {"name": "Academic Tutoring Center", "nameGeorgian": "აკადემიური რეპეტიტორობის ცენტრი", "location": "Library Building, 2nd Floor", "hours": "Monday-Friday: 9:00-18:00", "services": ["Math tutoring", "Writing support", "Study skills", "Exam preparation"], "contact": "<EMAIL>", "cost": "Free for students"}, "library": {"name": "University Library", "nameGeorgian": "უნივერსიტეტის ბიბლიოთეკა", "location": "Central Campus", "hours": "Monday-Friday: 8:00-22:00, Saturday: 9:00-18:00", "collections": {"books": 50000, "digitalResources": 15000, "journals": 200, "databases": 25}, "services": ["Book lending", "Research assistance", "Computer access", "Study rooms"], "contact": "<EMAIL>"}}, "studentLife": {"housing": {"dormitories": {"capacity": 300, "rooms": "Single and double occupancy", "amenities": ["WiFi", "<PERSON><PERSON><PERSON>", "Common areas", "24/7 security"], "cost": {"single": 800, "double": 600, "currency": "GEL per semester"}, "application": "<EMAIL>"}}, "dining": {"cafeteria": {"name": "University Cafeteria", "nameGeorgian": "უნივერსიტეტის კაფეტერია", "location": "Student Center", "hours": "Monday-Friday: 7:30-19:00", "mealPlans": {"full": 450, "partial": 300, "currency": "GEL per semester"}}}, "clubs": [{"name": "Computer Science Club", "nameGeorgian": "კომპიუტერული მეცნიერებების კლუბი", "activities": ["Programming competitions", "Tech talks", "Hackathons"], "contact": "<EMAIL>"}, {"name": "Business Club", "nameGeorgian": "ბიზნეს კლუბი", "activities": ["Case competitions", "Networking events", "Guest speakers"], "contact": "<EMAIL>"}, {"name": "Cultural Club", "nameGeorgian": "კულტურული კლუბი", "activities": ["Traditional dance", "Music performances", "Cultural festivals"], "contact": "<EMAIL>"}]}, "health": {"medicalCenter": {"name": "Student Health Center", "nameGeorgian": "სტუდენტური ჯანმრთელობის ცენტრი", "location": "Campus Medical Building", "hours": "Monday-Friday: 9:00-17:00", "services": ["General medicine", "Mental health counseling", "Emergency care"], "contact": "<EMAIL>", "insurance": "Required for all students"}}}, "campusFacilities": {"buildings": {"mainBuilding": {"name": "Main Academic Building", "nameGeorgian": "მთავარი აკადემიური შენობა", "floors": 4, "facilities": ["Classrooms", "Lecture halls", "Faculty offices", "Administration"], "accessibility": "Wheelchair accessible"}, "libraryBuilding": {"name": "Library and Research Building", "nameGeorgian": "ბიბლიოთეკისა და კვლევის შენობა", "floors": 3, "facilities": ["Library", "Study rooms", "Computer labs", "Research centers"], "capacity": 500}, "studentCenter": {"name": "Student Center", "nameGeorgian": "სტუდენტური ცენტრი", "floors": 2, "facilities": ["Cafeteria", "Recreation room", "Student organizations", "Events hall"], "hours": "Monday-Friday: 7:00-22:00"}}, "technology": {"wifi": {"coverage": "100% campus coverage", "network": "SOU-WiFi", "speed": "High-speed internet", "access": "Free for students and staff"}, "computerLabs": {"count": 5, "computers": 150, "software": ["Microsoft Office", "Programming IDEs", "Design software", "Statistical packages"], "hours": "Monday-Friday: 8:00-20:00"}}, "parking": {"studentParking": {"spaces": 200, "cost": "Free", "location": "Behind main building"}, "facultyParking": {"spaces": 50, "location": "Front of main building"}}}, "events": {"academic": {"orientation": {"name": "New Student Orientation", "nameGeorgian": "ახალი სტუდენტების ორიენტაცია", "date": "First week of September", "duration": "3 days", "activities": ["Campus tour", "Academic advising", "Registration assistance", "Social events"]}, "graduation": {"name": "Graduation Ceremony", "nameGeorgian": "დამთავრების ცერემონია", "date": "End of June", "location": "University Auditorium", "attendance": "Graduates, families, faculty"}}, "cultural": {"culturalWeek": {"name": "Georgian Culture Week", "nameGeorgian": "ქართული კულტურის კვირეული", "date": "October", "activities": ["Traditional performances", "Art exhibitions", "Food festival", "Academic conferences"]}}, "research": {"researchDay": {"name": "Annual Research Day", "nameGeorgian": "წლიური კვლევითი დღე", "date": "April", "activities": ["Student presentations", "Faculty research showcase", "Poster sessions", "Awards ceremony"]}}}, "policies": {"academic": {"grading": {"scale": "A (90-100), B (80-89), C (70-79), D (60-69), F (0-59)", "gpaCalculation": "4.0 scale", "minimumGPA": 2.0, "probation": "GPA below 2.0 for two consecutive semesters"}, "attendance": {"requirement": "Minimum 75% attendance required", "consequences": "Below 75% may result in course failure", "excusedAbsences": "Medical, family emergency, official university business"}}, "conduct": {"academicIntegrity": {"policy": "Zero tolerance for plagiarism and cheating", "consequences": "Warning, course failure, or expulsion depending on severity", "appeals": "Academic integrity committee review process"}, "studentConduct": {"expectations": "Respectful behavior, professional conduct, adherence to university values", "violations": "Harassment, discrimination, violence, substance abuse", "disciplinary": "Warning, probation, suspension, or expulsion"}}}, "contact": {"main": {"address": "University Street 1, Sokhumi, Georgia", "addressGeorgian": "უნივერსიტეტის ქუჩა 1, სოხუმი, საქართველო", "phone": "+995 443 123456", "fax": "+995 443 123457", "email": "<EMAIL>", "website": "www.sou.edu.ge"}, "departments": {"admissions": {"phone": "+995 443 123458", "email": "<EMAIL>", "office": "Main Building, Room 101"}, "studentServices": {"phone": "+995 443 123459", "email": "<EMAIL>", "office": "Student Center, Room 201"}, "registrar": {"phone": "+995 443 123460", "email": "<EMAIL>", "office": "Main Building, Room 102"}, "financialAid": {"phone": "+995 443 123461", "email": "<EMAIL>", "office": "Main Building, Room 103"}}, "emergencyContacts": {"campusSecurity": "+995 443 123470", "medicalEmergency": "+995 443 123471", "maintenance": "+995 443 123472"}}}