# GitLab Deployment Guide

This document provides step-by-step instructions for deploying this project to GitLab Pages.

## Prerequisites

- A GitLab account
- Git installed on your local machine

## Deployment Steps

1. **Create a new GitLab repository**

   - Go to GitLab and log in to your account
   - Click on "New project" or "Create new project"
   - Choose "Create blank project"
   - Name your repository (e.g., "e-commerce-chat")
   - Set visibility level (Public, Internal, or Private)
   - Click "Create project"

2. **Initialize Git in your local project (if not already done)**

   ```bash
   git init
   ```

3. **Add the GitLab repository as a remote**

   ```bash
   git remote add origin https://gitlab.com/your-username/e-commerce-chat.git
   ```

4. **Commit your changes**

   ```bash
   git add .
   git commit -m "Initial commit"
   ```

5. **Push to GitLab**

   ```bash
   git push -u origin main
   ```

   Note: If your default branch is named "master" instead of "main", use:
   ```bash
   git push -u origin master
   ```

6. **Verify CI/CD Pipeline**

   - Go to your GitLab repository
   - Navigate to "CI/CD" > "Pipelines"
   - You should see a pipeline running
   - Wait for it to complete successfully

7. **Access Your Deployed Site**

   Once the pipeline completes successfully, your site will be available at:
   ```
   https://your-username.gitlab.io/e-commerce-chat/
   ```

## Troubleshooting

- **Pipeline Fails**: Check the pipeline logs for errors. Common issues include:
  - Missing dependencies
  - Build errors
  - Incorrect configuration

- **Base Path Issues**: If your site loads but assets are missing, check that the `base` path in `vite.config.ts` matches your repository name.

- **Custom Domain**: To use a custom domain, see GitLab's documentation on [custom domains for GitLab Pages](https://docs.gitlab.com/ee/user/project/pages/custom_domains_ssl_tls_certification/).
