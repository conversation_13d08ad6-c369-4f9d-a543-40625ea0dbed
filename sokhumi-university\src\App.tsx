import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Layout/Header';
import Footer from './components/Layout/Footer';
import Home from './pages/Home';
import About from './pages/About';
import Academics from './pages/Academics';
import Admissions from './pages/Admissions';
import StudentLife from './pages/StudentLife';
import Research from './pages/Research';
import Faculty from './pages/Faculty';
import News from './pages/News';
import Contact from './pages/Contact';
import Library from './pages/Library';
import Chat from './components/Chat';
import './App.css';

function App() {
  return (
    <Router>
      <div className="app">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/academics" element={<Academics />} />
            <Route path="/admissions" element={<Admissions />} />
            <Route path="/student-life" element={<StudentLife />} />
            <Route path="/research" element={<Research />} />
            <Route path="/faculty" element={<Faculty />} />
            <Route path="/news" element={<News />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/library" element={<Library />} />
          </Routes>
        </main>
        <Footer />
<Chat
          config={{
            theme: {
              primaryColor: '#1e40af',
              secondaryColor: '#f59e0b',
              textColor: '#1f2937',
              backgroundColor: '#ffffff',
              position: 'right',
              initialState: 'minimized',
              greeting: 'გამარჯობა! მე ვარ Chester, სოხუმის სახელმწიფო უნივერსიტეტის ასისტენტი. როგორ შემიძლია დაგეხმაროთ?'
            },
            agentName: 'Chester',
            enableSound: true,
            enableNotifications: true,
            enableAttachments: true,
            enableHistory: true
          }}
        />
      </div>
    </Router>
  );
}

export default App;
