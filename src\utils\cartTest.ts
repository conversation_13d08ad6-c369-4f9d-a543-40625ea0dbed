import { CartStorageService } from '../services/cartStorage';
import type { CartItem } from '../types/cart';

/**
 * Test utility to verify cart persistence functionality
 */
export const testCartPersistence = async (): Promise<void> => {
  console.log('🧪 Testing Cart Persistence...');

  try {
    // Clear any existing cart data
    await CartStorageService.clearCart();
    console.log('✅ Cleared existing cart data');

    // Test 1: Save a test item
    const testItem: CartItem = {
      id: 'test-item-1',
      productId: 'p121',
      name: 'UltraMax Pro Flagship Smartphone',
      price: 1299.99,
      imageUrl: 'https://images.unsplash.com/photo-1598327105666-5b89351aff97?q=80&w=1000&auto=format&fit=crop',
      quantity: 1,
      addedAt: Date.now(),
      category: 'Electronics',
      description: 'Test smartphone for cart persistence'
    };

    const saveResult = await CartStorageService.saveCart([testItem]);
    console.log('✅ Save test item result:', saveResult);

    // Test 2: Load the item back
    const loadedItems = await CartStorageService.loadCart();
    console.log('✅ Loaded items:', loadedItems);

    // Test 3: Verify the item matches
    if (loadedItems.length === 1 && loadedItems[0].productId === 'p121') {
      console.log('✅ Cart persistence test PASSED!');
    } else {
      console.error('❌ Cart persistence test FAILED!');
      console.error('Expected 1 item with productId p121, got:', loadedItems);
    }

    // Test 4: Check localStorage directly
    const rawData = localStorage.getItem('premium-store-cart');
    console.log('✅ Raw localStorage data:', rawData);

    // Clean up
    await CartStorageService.clearCart();
    console.log('✅ Cleaned up test data');

  } catch (error) {
    console.error('❌ Cart persistence test ERROR:', error);
  }
};

/**
 * Test utility to check if specific products exist
 */
export const testProductExists = (productId: string): boolean => {
  // This would need to import allProducts, but we'll keep it simple for now
  console.log(`🔍 Testing if product ${productId} exists...`);
  
  // Check localStorage for any existing cart data
  const rawData = localStorage.getItem('premium-store-cart');
  if (rawData) {
    try {
      const cartData = JSON.parse(rawData);
      console.log('📦 Current cart data:', cartData);
    } catch (error) {
      console.error('❌ Failed to parse cart data:', error);
    }
  } else {
    console.log('📦 No cart data found in localStorage');
  }
  
  return true;
};

// Auto-run test when this module is imported in development
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
  // Add a global function for manual testing
  (window as any).testCartPersistence = testCartPersistence;
  (window as any).testProductExists = testProductExists;
  
  console.log('🧪 Cart test utilities loaded. Run testCartPersistence() or testProductExists("p121") in console.');
}
