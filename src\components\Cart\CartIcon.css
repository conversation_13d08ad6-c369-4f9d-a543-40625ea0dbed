.cart-icon-container {
  position: relative;
}

/* Cart dropdown overlay for proper layering */
.cart-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 9990; /* Above product overlay but below cart dropdown */
  pointer-events: auto;
}

.cart-icon-button {
  position: relative;
  transition: all var(--transition-fast);
}

.cart-icon-button.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.cart-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: linear-gradient(135deg, var(--error) 0%, #dc2626 100%);
  color: white;
  font-size: 0.7rem;
  font-weight: 700;
  min-width: 18px;
  height: 18px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--space-1);
  box-shadow: var(--shadow-md);
  border: 2px solid white;
  animation: cartBadgeAppear 0.3s ease-out;
}

.cart-loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.cart-dropdown-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 999;
}

@keyframes cartBadgeAppear {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Loading spinner styles */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--neutral-200);
  border-top: 2px solid var(--primary-600);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 12px;
  height: 12px;
  border-width: 1.5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
