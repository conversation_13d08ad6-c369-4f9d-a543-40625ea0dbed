import type { <PERSON> } from 'react';
import { ChatProvider } from '../../context/ChatContext';
import ChatBubble from './ChatBubble';
import ChatWindow from './ChatWindow';
import type { ChatConfig } from '../../types/chat';
import '../../styles/chat.css';

interface ChatProps {
  config?: Partial<ChatConfig>;
}

const Chat: FC<ChatProps> = ({ config }) => {
  return (
    <ChatProvider config={config}>
      <div className="chat-container">
        <ChatBubble />
        <ChatWindow />
      </div>
    </ChatProvider>
  );
};

export default Chat;
