import type { FC } from 'react';
import { ChatProvider } from '../../context/ChatContext';
import ChatBubble from './ChatBubble';
import ChatWindow from './ChatWindow';

interface ChatProps {
  config?: {
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      textColor?: string;
      backgroundColor?: string;
      position?: 'right' | 'left';
      greeting?: string;
    };
    agentName?: string;
    enableSound?: boolean;
    enableNotifications?: boolean;
    enableAttachments?: boolean;
    enableHistory?: boolean;
  };
}

const Chat: FC<ChatProps> = ({ config }) => {
  return (
    <ChatProvider config={config}>
      <ChatBubble />
      <ChatWindow />
    </ChatProvider>
  );
};

export default Chat;
