import React from 'react';

interface ChatProps {
  config?: {
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
      textColor?: string;
      backgroundColor?: string;
      position?: 'right' | 'left';
      initialState?: 'minimized' | 'expanded';
      greeting?: string;
    };
    agentName?: string;
    enableSound?: boolean;
    enableNotifications?: boolean;
    enableAttachments?: boolean;
    enableHistory?: boolean;
  };
}

const Chat: React.FC<ChatProps> = ({ config }) => {
  const [isOpen, setIsOpen] = React.useState(false);

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="chat-container">
      {/* Chat Bubble */}
      <div
        className="chat-bubble"
        onClick={toggleChat}
        style={{
          position: 'fixed',
          bottom: '20px',
          [config?.theme?.position || 'right']: '20px',
          width: '60px',
          height: '60px',
          backgroundColor: config?.theme?.primaryColor || '#1e40af',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          zIndex: 1000,
          color: 'white',
          fontSize: '24px'
        }}
      >
        💬
      </div>

      {/* Chat Window */}
      {isOpen && (
        <div
          className="chat-window"
          style={{
            position: 'fixed',
            bottom: '90px',
            [config?.theme?.position || 'right']: '20px',
            width: '350px',
            height: '500px',
            backgroundColor: config?.theme?.backgroundColor || '#ffffff',
            borderRadius: '12px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.2)',
            zIndex: 999,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
          }}
        >
          {/* Header */}
          <div
            style={{
              backgroundColor: config?.theme?.primaryColor || '#1e40af',
              color: 'white',
              padding: '16px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}
          >
            <h3 style={{ margin: 0, fontSize: '16px' }}>
              {config?.agentName || 'Chester'}
            </h3>
            <button
              onClick={toggleChat}
              style={{
                background: 'none',
                border: 'none',
                color: 'white',
                fontSize: '18px',
                cursor: 'pointer'
              }}
            >
              ✕
            </button>
          </div>

          {/* Messages */}
          <div
            style={{
              flex: 1,
              padding: '16px',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '12px'
            }}
          >
            <div
              style={{
                backgroundColor: config?.theme?.secondaryColor || '#f59e0b',
                color: config?.theme?.textColor || '#1f2937',
                padding: '12px',
                borderRadius: '12px',
                maxWidth: '80%',
                alignSelf: 'flex-start'
              }}
            >
              {config?.theme?.greeting || 'გამარჯობა! როგორ შემიძლია დაგეხმაროთ?'}
            </div>
          </div>

          {/* Input */}
          <div
            style={{
              padding: '16px',
              borderTop: '1px solid #e5e7eb'
            }}
          >
            <input
              type="text"
              placeholder="Type your message..."
              style={{
                width: '100%',
                padding: '12px',
                border: '1px solid #d1d5db',
                borderRadius: '8px',
                outline: 'none',
                fontSize: '14px'
              }}
              onFocus={(e) => {
                e.target.style.borderColor = config?.theme?.primaryColor || '#1e40af';
              }}
              onBlur={(e) => {
                e.target.style.borderColor = '#d1d5db';
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default Chat;
