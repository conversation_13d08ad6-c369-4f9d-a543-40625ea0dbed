:root {
  /* Dark theme colors */
  --chat-primary-color: #7c5cff;
  --chat-secondary-color: #2a2a3a;
  --chat-text-color: #e0e0e0;
  --chat-background-color: #1e1e2d;
  --chat-user-message-bg: #7c5cff;
  --chat-user-message-text: #ffffff;
  --chat-agent-message-bg: #2a2a3a;
  --chat-agent-message-text: #e0e0e0;
  --chat-input-bg: #2a2a3a;
  --chat-input-border: #3a3a4a;
  --chat-header-bg: #1a1a27;
  --chat-header-text: #ffffff;
  --chat-bubble-bg: #7c5cff;
  --chat-bubble-text: #ffffff;
  --chat-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  --chat-border-radius: 16px;
  --chat-message-border-radius: 18px;
  --chat-transition-speed: 0.3s;
  --chat-font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes typing {
  0% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.3;
  }
}

/* Utility classes */
.chat-hidden {
  display: none !important;
}

.chat-visible {
  display: flex !important;
}

/* Accessibility focus styles */
.chat-focus-visible:focus {
  outline: 2px solid var(--chat-primary-color);
  outline-offset: 2px;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  :root {
    --chat-border-radius: 12px;
    --chat-message-border-radius: 14px;
  }
}
