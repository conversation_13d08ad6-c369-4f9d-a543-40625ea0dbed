import { Home, Coffee, Trophy, Heart } from 'lucide-react';

const StudentLife = () => {
  return (
    <div className="student-life-page">
      <section className="page-header">
        <div className="container">
          <h1>სტუდენტური ცხოვრება / Student Life</h1>
          <p>კამპუსის ცხოვრება და აქტივობები</p>
          <p>Campus life and activities</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="facilities-grid grid grid-2">
            <div className="facility-card card">
              <Home size={40} />
              <h3>საცხოვრებელი / Dormitories</h3>
              <p>თანამედროვე საცხოვრებელი შენობები</p>
              <p>Modern dormitory buildings</p>
            </div>
            <div className="facility-card card">
              <Coffee size={40} />
              <h3>კაფეტერია / Cafeteria</h3>
              <p>მრავალფეროვანი და ჯანსაღი კვება</p>
              <p>Diverse and healthy food options</p>
            </div>
            <div className="facility-card card">
              <Trophy size={40} />
              <h3>სპორტი / Sports</h3>
              <p>სპორტული დარბაზები და მოედნები</p>
              <p>Sports halls and courts</p>
            </div>
            <div className="facility-card card">
              <Heart size={40} />
              <h3>კლუბები / Clubs</h3>
              <p>სტუდენტური ორგანიზაციები</p>
              <p>Student organizations</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default StudentLife;
