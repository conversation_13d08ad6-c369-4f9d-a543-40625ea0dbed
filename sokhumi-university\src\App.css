/* Import Professional Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Georgian:wght@300;400;500;600;700;800;900&display=swap');

/* Advanced University Website Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #1e40af; /* Blue 700 - Better contrast */
  --primary-dark: #1e3a8a; /* Blue 800 - For hover states */
  --secondary-color: #0f172a; /* Slate 900 - High contrast */
  --accent-color: #f59e0b; /* Amber 500 */
  --accent-light: #fef3c7; /* Amber 100 */
  --white: #ffffff; /* Missing white variable */
  --text-dark: #0f172a;
  --text-medium: #334155;
  --text-light: #475569; /* Darker for better contrast */
  --text-muted: #64748b; /* Darker for better contrast */
  --background-primary: #ffffff;
  --background-secondary: #f8fafc;
  --background-accent: #f1f5f9;
  --border-color: #e2e8f0;
  --border-light: #f1f5f9;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --gradient-accent: linear-gradient(135deg, var(--secondary-color) 0%, var(--accent-color) 100%);
  --border-radius-sm: 0.375rem;
  --border-radius: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
  font-family: 'Noto Sans Georgian', 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.65;
  color: var(--text-dark);
  background-color: var(--background-primary);
  font-size: 16px;
  font-weight: 400;
  letter-spacing: -0.01em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.main-content {
  flex: 1;
  width: 100%;
  overflow-x: hidden;
}

/* Container System - Full Width Layout */
.container {
  max-width: 100%;
  margin: 0 auto;
  padding: 0 1.5rem;
  width: 100%;
}

.container-content {
  max-width: 1600px;
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

/* Enhanced Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 700;
  line-height: 1.25;
  margin-bottom: 1.25rem;
  letter-spacing: -0.025em;
  text-rendering: optimizeLegibility;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  color: var(--secondary-color);
  font-weight: 800;
  line-height: 1.1;
  letter-spacing: -0.04em;
  margin-bottom: 1.5rem;
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  color: var(--secondary-color);
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.03em;
  margin-bottom: 1.25rem;
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  color: var(--text-dark);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.02em;
  margin-bottom: 1rem;
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  color: var(--text-dark);
  font-weight: 600;
  line-height: 1.35;
  letter-spacing: -0.015em;
  margin-bottom: 0.875rem;
}

h5 {
  font-size: 1.25rem;
  color: var(--text-dark);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 0.75rem;
}

h6 {
  font-size: 1.125rem;
  color: var(--text-medium);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

p {
  margin-bottom: 1.25rem;
  color: var(--text-light);
  line-height: 1.7;
  font-size: 1rem;
  font-weight: 400;
  letter-spacing: -0.005em;
}

.lead {
  font-size: 1.25rem;
  font-weight: 400;
  line-height: 1.6;
  color: var(--text-medium);
  margin-bottom: 1.5rem;
}

.small {
  font-size: 0.875rem;
  line-height: 1.5;
  color: var(--text-muted);
}

/* Enhanced Button Typography & Design */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.875rem 2rem;
  border: 2px solid transparent;
  border-radius: 0.75rem;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 600;
  font-size: 1rem;
  line-height: 1.4;
  letter-spacing: -0.01em;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border: 2px solid var(--primary-color);
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: var(--white);
  border: 2px solid var(--secondary-color);
}

.btn-secondary:hover {
  background-color: #0f172a;
  border-color: #0f172a;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  color: var(--white);
}

.btn-outline {
  background-color: var(--white);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline:hover {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* Focus states for accessibility */
.btn:focus {
  outline: 3px solid var(--accent-color);
  outline-offset: 2px;
}

.nav-link:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
  border-radius: 4px;
}

.program-link:focus,
.news-link:focus {
  outline: 2px solid var(--accent-color);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Cards */
.card {
  background: var(--white);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: var(--shadow);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* Grid */
.grid {
  display: grid;
  gap: 2rem;
}

.grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* Sections */
.section {
  padding: 4rem 0;
}

.section-alt {
  background-color: var(--background-secondary);
}

/* Research Page Styles */
.research-page {
  min-height: 100vh;
}

.research-hero {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  color: white;
  padding: 6rem 0 4rem;
  position: relative;
  overflow: hidden;
}

.research-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  opacity: 0.3;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.hero-content h1 {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0.9;
  font-weight: 300;
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
  margin-top: 3rem;
}

.stat-item {
  text-align: center;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius-lg);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
  display: block;
  font-size: 3rem;
  font-weight: 800;
  color: var(--accent-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 1.1rem;
  opacity: 0.9;
  font-weight: 500;
}

.section-header {
  text-align: center;
  margin-bottom: 4rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.section-header h2 {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
  font-weight: 700;
}

.section-header p {
  font-size: 1.2rem;
  color: var(--text-medium);
  line-height: 1.6;
}

.research-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2.5rem;
  margin-bottom: 2rem;
}

.research-card {
  background: white;
  border-radius: var(--border-radius-xl);
  padding: 2.5rem;
  box-shadow: var(--shadow-lg);
  transition: var(--transition);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
}

.research-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--secondary-color), var(--accent-color));
}

.research-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
  border-color: var(--secondary-color);
}

.card-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
  border-radius: var(--border-radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
}

.research-card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--secondary-color);
  font-weight: 600;
}

.research-card p {
  color: var(--text-light);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.feature-list {
  list-style: none;
  padding: 0;
}

.feature-list li {
  padding: 0.5rem 0;
  color: var(--text-medium);
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
}

.focus-areas {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.focus-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius-lg);
  text-align: center;
  box-shadow: var(--shadow);
  transition: var(--transition);
  border: 1px solid var(--border-color);
}

.focus-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--secondary-color);
}

.focus-card svg {
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.focus-card h4 {
  font-size: 1.25rem;
  margin-bottom: 0.75rem;
  color: var(--secondary-color);
  font-weight: 600;
}

.focus-card p {
  color: var(--text-light);
  font-size: 0.95rem;
  margin: 0;
}

/* Enhanced Header Design */
.header {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(15, 23, 42, 0.08);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Add padding to main content to account for fixed header */
.home {
  padding-top: 80px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem 0;
  min-height: 80px;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  gap: 1.25rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo:hover {
  transform: translateY(-1px);
}

.logo-image {
  width: 64px;
  height: 64px;
  object-fit: contain;
  border-radius: 0.75rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo:hover .logo-image {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.logo-text h1 {
  font-size: 1.625rem;
  color: var(--secondary-color);
  margin: 0;
  line-height: 1.2;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.logo-text p {
  font-size: 0.875rem;
  color: var(--text-medium);
  margin: 0;
  font-weight: 500;
  letter-spacing: 0.01em;
}

.nav-desktop {
  display: none;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: 0.5rem;
  align-items: center;
  margin: 0;
  padding: 0;
}

.nav-link {
  text-decoration: none;
  color: var(--text-dark);
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.95rem;
  padding: 0.75rem 1.25rem;
  border-radius: 0.5rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 0.375rem;
  letter-spacing: -0.01em;
  position: relative;
  white-space: nowrap;
}

.nav-link::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary-color);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(-50%);
}

.nav-link:hover,
.nav-link.active {
  color: var(--primary-color);
  background-color: rgba(30, 64, 175, 0.05);
}

.nav-link:hover::before,
.nav-link.active::before {
  width: 80%;
}

.nav-dropdown {
  position: relative;
}

.dropdown-toggle {
  background: none;
  border: none;
  cursor: pointer;
  font-size: inherit;
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 0.5rem);
  left: 0;
  background: var(--white);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(15, 23, 42, 0.08);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0.75rem;
  padding: 0.75rem;
  min-width: 220px;
  z-index: 1001;
  opacity: 0;
  transform: translateY(-10px);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
}

.nav-dropdown:hover .dropdown-menu {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.dropdown-link {
  display: block;
  padding: 0.75rem 1rem;
  text-decoration: none;
  color: var(--text-dark);
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 500;
  font-size: 0.9rem;
  border-radius: 0.5rem;
  letter-spacing: -0.01em;
}

.dropdown-link:hover {
  background-color: rgba(30, 64, 175, 0.05);
  color: var(--primary-color);
  transform: translateX(4px);
}

.mobile-menu-btn {
  display: block;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--text-dark);
  padding: 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-btn:hover {
  background-color: rgba(30, 64, 175, 0.05);
  color: var(--primary-color);
  transform: scale(1.05);
}

.nav-mobile {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px) saturate(180%);
  border-top: 1px solid rgba(15, 23, 42, 0.08);
  padding: 1.5rem 0;
  margin-top: 1rem;
  border-radius: 0 0 1rem 1rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.nav-list-mobile {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.nav-link-mobile {
  display: block;
  padding: 1rem 1.25rem;
  text-decoration: none;
  color: var(--text-dark);
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 500;
  font-size: 1rem;
  border-radius: 0.5rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  letter-spacing: -0.01em;
  margin: 0 1rem;
}

.nav-link-mobile:hover {
  color: var(--primary-color);
  background-color: rgba(30, 64, 175, 0.05);
  transform: translateX(8px);
}

/* Desktop Navigation */
@media (min-width: 769px) {
  .nav-desktop {
    display: block;
  }

  .mobile-menu-btn {
    display: none;
  }

  .nav-mobile {
    display: none;
  }

  .logo-text h1 {
    font-size: 1.8rem;
  }

  .logo-text p {
    font-size: 1rem;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1.5rem;
  }

  .research-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
  }

  .hero-content h1 {
    font-size: 3.5rem;
  }

  .section-header h2 {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .grid {
    gap: 1rem;
  }

  .section {
    padding: 3rem 0;
  }

  .logo-text h1 {
    font-size: 1.2rem;
  }

  .logo-text p {
    font-size: 0.8rem;
  }

  /* Research Page Mobile */
  .research-hero {
    padding: 4rem 0 3rem;
  }

  .hero-content h1 {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
  }

  .hero-stats {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 2rem;
  }

  .stat-item {
    padding: 1.5rem;
  }

  .stat-number {
    font-size: 2.5rem;
  }

  .section-header h2 {
    font-size: 2rem;
  }

  .section-header p {
    font-size: 1rem;
  }

  .research-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .research-card {
    padding: 2rem;
  }

  .card-icon {
    width: 60px;
    height: 60px;
  }

  .focus-areas {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }

  .focus-card {
    padding: 1.5rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }

  .section-header h2 {
    font-size: 1.75rem;
  }

  .research-card {
    padding: 1.5rem;
  }

  .card-icon {
    width: 50px;
    height: 50px;
  }

  .focus-areas {
    grid-template-columns: 1fr;
  }
}

/* Footer Styles */
.footer {
  background-color: var(--text-dark);
  color: var(--white);
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  padding: 3rem 0 2rem;
}

.footer-section h3,
.footer-section h4,
.footer-section h5 {
  color: var(--white);
  margin-bottom: 1rem;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.footer-logo-image {
  width: 50px;
  height: 50px;
  object-fit: contain;
  border-radius: 0.5rem;
}

.footer-description {
  color: #d1d5db;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.footer-links {
  list-style: none;
  padding: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent-color);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.contact-item svg {
  color: var(--accent-color);
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.contact-item p {
  margin: 0;
  color: #d1d5db;
  line-height: 1.4;
}

.social-media h5 {
  margin-bottom: 0.75rem;
}

.social-links {
  display: flex;
  gap: 1rem;
}

.social-links a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-links a:hover {
  background-color: var(--accent-color);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding: 1.5rem 0;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-bottom p {
  color: #d1d5db;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 2rem;
}

.footer-bottom-links a {
  color: #d1d5db;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: var(--accent-color);
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 0 1rem;
  }

  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }

  .footer-bottom-links {
    gap: 1rem;
  }
}

/* Home Page Styles - Full Width */
.hero {
  position: relative;
  min-height: 70vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  color: var(--white);
  width: 100%;
  margin: 0;
  padding: 0;
}

.hero-background {
  position: relative;
  width: 100%;
  z-index: 1;
}

.hero-content {
  text-align: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 2rem;
  width: 100%;
}

.hero-title {
  font-size: clamp(2.5rem, 6vw, 4.5rem);
  font-weight: 800;
  margin-bottom: 1.5rem;
  color: var(--white);
  line-height: 1.1;
  letter-spacing: -0.04em;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: clamp(1.5rem, 4vw, 2.25rem);
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--accent-color);
  line-height: 1.2;
  letter-spacing: -0.02em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.hero-description {
  font-size: clamp(1rem, 2.5vw, 1.25rem);
  margin-bottom: 2rem;
  color: rgba(255, 255, 255, 0.95);
  line-height: 1.7;
  letter-spacing: -0.01em;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* Stats Section */
.stats-section {
  background: var(--white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 2rem;
  background: var(--white);
  border-radius: 1rem;
  box-shadow: var(--shadow);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  border-radius: 50%;
  color: var(--white);
}

.stat-content h3 {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--primary-color);
  margin: 0;
}

.stat-content p {
  font-size: 1rem;
  color: var(--text-light);
  margin: 0;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-header h2 {
  margin-bottom: 1rem;
}

.section-header p {
  font-size: 1.1rem;
  color: var(--text-light);
  margin-bottom: 0.5rem;
}

/* Programs */
.programs-grid {
  margin-top: 2rem;
}

.program-card {
  text-align: center;
  padding: 2rem;
}

.program-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.program-card h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.program-card p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.program-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 600;
  font-size: 0.95rem;
  margin-top: 1.25rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background-color: rgba(30, 64, 175, 0.05);
  letter-spacing: -0.01em;
}

.program-link:hover {
  color: var(--primary-dark);
  background-color: rgba(30, 64, 175, 0.1);
  transform: translateY(-2px);
}

/* News & Events */
.news-grid {
  margin-top: 2rem;
}

.news-card {
  padding: 2rem;
}

.news-meta {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-light);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.news-card h3 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.news-card h4 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.news-card p {
  margin-bottom: 0.5rem;
  line-height: 1.6;
}

.news-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--primary-color);
  text-decoration: none;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
  font-weight: 600;
  font-size: 0.95rem;
  margin-top: 1.25rem;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  background-color: rgba(30, 64, 175, 0.05);
  letter-spacing: -0.01em;
}

.news-link:hover {
  color: var(--primary-dark);
  background-color: rgba(30, 64, 175, 0.1);
  transform: translateY(-2px);
}

/* Call to Action */
.cta-section {
  text-align: center;
}

.cta-content h2 {
  margin-bottom: 0.5rem;
}

.cta-content h3 {
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

.cta-content p {
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* Responsive Home Page */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2.5rem;
  }

  .hero-subtitle {
    font-size: 2rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 1.5rem;
  }

  .cta-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* Page Header Styles */
.page-header {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
  color: var(--white);
  padding: 4rem 0 2rem;
  text-align: center;
}

.page-header h1 {
  color: var(--white);
  margin-bottom: 1rem;
}

.page-header p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 0.5rem;
}

/* Content Styles */
.content-grid {
  align-items: center;
  gap: 3rem;
}

.content-text {
  order: 1;
}

.content-image {
  order: 2;
}

.about-image {
  width: 100%;
  height: 300px;
  object-fit: cover;
  border-radius: 1rem;
}

.icon-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.icon-header svg {
  color: var(--primary-color);
}

/* Mission & Vision Cards */
.mission-vision-grid {
  gap: 2rem;
}

.mission-card,
.vision-card {
  text-align: center;
}

/* Values Grid */
.values-grid {
  margin-top: 2rem;
}

.value-card {
  text-align: center;
  padding: 2rem 1rem;
}

.value-card svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Leadership */
.leadership-grid {
  margin-top: 2rem;
}

.leader-card {
  text-align: center;
  padding: 2rem;
}

.leader-image {
  margin-bottom: 1.5rem;
}

.placeholder-image {
  width: 120px;
  height: 120px;
  background: var(--background-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  color: var(--text-light);
}

/* Faculty Cards */
.faculties-grid,
.degrees-grid,
.calendar-grid,
.requirements-grid {
  margin-top: 2rem;
}

.faculty-card,
.degree-card,
.semester-card,
.requirement-card {
  text-align: center;
  padding: 2rem;
}

.faculty-card svg,
.degree-card svg,
.semester-card svg,
.requirement-card svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.faculty-card ul {
  list-style: none;
  padding: 0;
  margin-top: 1rem;
}

.faculty-card li {
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.faculty-card li:last-child {
  border-bottom: none;
}

/* Process Cards */
.process-grid {
  margin-top: 2rem;
}

.process-card {
  text-align: center;
  padding: 2rem;
}

.process-card svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Facility Cards */
.facilities-grid,
.research-grid {
  margin-top: 2rem;
}

.facility-card,
.research-card {
  text-align: center;
  padding: 2rem;
}

.facility-card svg,
.research-card svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

/* Faculty Page */
.faculty-grid {
  margin-top: 2rem;
}

.faculty-card {
  text-align: center;
  padding: 2rem;
}

.faculty-image {
  margin-bottom: 1.5rem;
}

.faculty-image svg {
  color: var(--text-light);
}

.faculty-card .contact-info {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.faculty-card .contact-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  font-size: 0.9rem;
  color: var(--text-light);
}

/* Library Cards */
.library-grid {
  margin-top: 2rem;
}

.library-card {
  text-align: center;
  padding: 2rem;
}

.library-card svg {
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.library-card .btn {
  margin-top: 1rem;
}

/* Contact Page */
.contact-grid {
  gap: 3rem;
  align-items: flex-start;
}

.contact-info .contact-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 2rem;
  padding: 1.5rem;
  background: var(--background-light);
  border-radius: 0.75rem;
}

.contact-info .contact-item svg {
  color: var(--primary-color);
  margin-top: 0.25rem;
  flex-shrink: 0;
}

.contact-info .contact-item h4 {
  margin-bottom: 0.5rem;
  color: var(--primary-color);
}

.contact-info .contact-item p {
  margin-bottom: 0.25rem;
  color: var(--text-light);
}

/* Contact Form */
.contact-form {
  background: var(--white);
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: var(--shadow);
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid var(--border-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Map */
.map-container {
  margin-top: 2rem;
}

.map-placeholder {
  height: 400px;
  background: var(--background-light);
  border-radius: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
}

.map-placeholder svg {
  margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .content-grid {
    gap: 2rem;
  }

  .content-text {
    order: 2;
  }

  .content-image {
    order: 1;
  }

  .contact-grid {
    gap: 2rem;
  }

  .contact-form {
    padding: 1.5rem;
  }

  .icon-header {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .values-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .leadership-grid,
  .faculty-grid {
    grid-template-columns: 1fr;
  }
}
