import React, { createContext, useContext, useReducer, useEffect } from 'react';
import type { ReactNode, FC } from 'react';
import { v4 as uuidv4 } from 'uuid';
import type { ChatState, Message, ChatConfig, MessageStatus } from '../types/chat';
import { loadMessages, saveMessages, clearMessages, loadLastReadTimestamp, saveLastReadTimestamp } from '../utils/storage';
import {
  getUniversityAIResponse,
  formatChatHistoryForUniversity
} from '../services/universityAIService';
import { GeorgianTranslationService } from '../services/georgianTranslation';

// Define the context type
interface ChatContextType {
  state: ChatState;
  config: ChatConfig;
  sendMessage: (content: string) => void;
  sendImage: (file: File) => void;
  toggleChat: () => void;
  markAllAsRead: () => void;
  setTyping: (isTyping: boolean) => void;
  clearHistory: () => void;
  updateConfig: (newConfig: Partial<ChatConfig>) => void;
  setDraftMessage: (message: string) => void;
}

// Create the context with a default value
const ChatContext = createContext<ChatContextType | undefined>(undefined);

// Define action types
type ChatAction =
  | { type: 'TOGGLE_CHAT' }
  | { type: 'SEND_MESSAGE'; payload: Message }
  | { type: 'RECEIVE_MESSAGE'; payload: Message }
  | { type: 'UPDATE_MESSAGE_STATUS'; payload: { id: string; status: MessageStatus } }
  | { type: 'MARK_ALL_READ' }
  | { type: 'SET_TYPING'; payload: boolean }
  | { type: 'CLEAR_HISTORY' }
  | { type: 'LOAD_HISTORY'; payload: Message[] }
  | { type: 'SET_LAST_READ'; payload: number }
  | { type: 'SET_DRAFT_MESSAGE'; payload: string };

// Define the initial state
const initialState: ChatState = {
  isOpen: false,
  messages: [],
  unreadCount: 0,
  isTyping: false,
  lastReadTimestamp: null,
  draftMessage: '',
};

// Default configuration with university colors
const defaultConfig: ChatConfig = {
  theme: {
    primaryColor: '#1e40af', // University blue
    secondaryColor: '#f59e0b', // University amber
    textColor: '#1f2937',
    backgroundColor: '#ffffff',
    position: 'right',
    initialState: 'minimized',
    greeting: 'გამარჯობა! როგორ შემიძლია დაგეხმაროთ სოხუმის სახელმწიფო უნივერსიტეტთან დაკავშირებით?',
  },
  agentName: 'University Assistant',
  enableSound: true,
  enableNotifications: true,
  enableAttachments: true,
  enableHistory: true,
};

// Reducer function
const chatReducer = (state: ChatState, action: ChatAction): ChatState => {
  switch (action.type) {
    case 'TOGGLE_CHAT':
      return {
        ...state,
        isOpen: !state.isOpen,
      };
    case 'SEND_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
      };
    case 'RECEIVE_MESSAGE':
      return {
        ...state,
        messages: [...state.messages, action.payload],
        unreadCount: state.isOpen ? state.unreadCount : state.unreadCount + 1,
      };
    case 'UPDATE_MESSAGE_STATUS':
      return {
        ...state,
        messages: state.messages.map((message) =>
          message.id === action.payload.id
            ? { ...message, status: action.payload.status }
            : message
        ),
      };
    case 'MARK_ALL_READ':
      return {
        ...state,
        unreadCount: 0,
        messages: state.messages.map((message) => ({
          ...message,
          isRead: true,
        })),
      };
    case 'SET_TYPING':
      return {
        ...state,
        isTyping: action.payload,
      };
    case 'CLEAR_HISTORY':
      return {
        ...state,
        messages: [],
        unreadCount: 0,
      };
    case 'LOAD_HISTORY':
      return {
        ...state,
        messages: action.payload,
        unreadCount: action.payload.filter(
          (message) => !message.isRead && message.sender === 'agent'
        ).length,
      };
    case 'SET_LAST_READ':
      return {
        ...state,
        lastReadTimestamp: action.payload,
      };
    case 'SET_DRAFT_MESSAGE':
      return {
        ...state,
        draftMessage: action.payload,
      };
    default:
      return state;
  }
};

// Provider component
interface ChatProviderProps {
  children: ReactNode;
  config?: Partial<ChatConfig>;
}

export const ChatProvider: FC<ChatProviderProps> = ({
  children,
  config: userConfig,
}) => {
  const [config, setConfig] = React.useState<ChatConfig>({
    ...defaultConfig,
    ...userConfig,
    theme: {
      ...defaultConfig.theme,
      ...(userConfig?.theme || {}),
    },
  });

  const [state, dispatch] = useReducer(chatReducer, initialState);

  // Load chat history from local storage on mount
  useEffect(() => {
    if (config.enableHistory) {
      const savedMessages = loadMessages();
      if (savedMessages.length > 0) {
        dispatch({ type: 'LOAD_HISTORY', payload: savedMessages });
      } else if (config.theme?.greeting) {
        // Add greeting message if no history exists
        const greetingMessage: Message = {
          id: uuidv4(),
          content: config.theme.greeting,
          sender: 'agent',
          timestamp: Date.now(),
          status: 'delivered',
          isRead: false,
        };
        dispatch({ type: 'RECEIVE_MESSAGE', payload: greetingMessage });
      }

      const lastRead = loadLastReadTimestamp();
      if (lastRead) {
        dispatch({ type: 'SET_LAST_READ', payload: lastRead });
      }
    }
  }, [config.enableHistory, config.theme?.greeting]);

  // Save messages to local storage when they change
  useEffect(() => {
    if (config.enableHistory && state.messages.length > 0) {
      saveMessages(state.messages);
    }
  }, [state.messages, config.enableHistory]);

  // Send a message
  const sendMessage = async (content: string) => {
    if (!content.trim()) return;

    // Detect language and translate if Georgian
    const isGeorgian = GeorgianTranslationService.detectLanguage(content) === 'ka';
    let translatedContent = content;

    if (isGeorgian) {
      console.log('🇬🇪 Georgian text detected, translating to English for AI...');
      const translationResult = await GeorgianTranslationService.translateToEnglish(content);
      translatedContent = translationResult.translatedText;
      console.log('🔄 Translation:', content, '→', translatedContent);
    }

    // Create and send user message (show original language to user)
    const userMessage: Message = {
      id: uuidv4(),
      content, // Show original language in chat
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending',
      isRead: true,
    };

    dispatch({ type: 'SEND_MESSAGE', payload: userMessage });

    // Update message status to sent
    setTimeout(() => {
      dispatch({
        type: 'UPDATE_MESSAGE_STATUS',
        payload: { id: userMessage.id, status: 'sent' },
      });

      setTimeout(() => {
        dispatch({
          type: 'UPDATE_MESSAGE_STATUS',
          payload: { id: userMessage.id, status: 'delivered' },
        });
      }, 500);
    }, 300);

    // Show typing indicator while waiting for AI response
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Format chat history for the API
      const chatHistory = formatChatHistoryForUniversity(state.messages.slice(-10));

      // Get response from University AI
      const aiResponse = await getUniversityAIResponse(
        translatedContent, // Send translated English to AI
        chatHistory
      );

      // Translate AI response back to Georgian if user sent Georgian
      let finalResponse = aiResponse;
      if (isGeorgian) {
        console.log('🇬🇪 Translating AI response back to Georgian...');
        const responseTranslation = await GeorgianTranslationService.translateToGeorgian(aiResponse);
        finalResponse = responseTranslation.translatedText;
        console.log('🔄 Response translation:', aiResponse, '→', finalResponse);
      }

      // Create and send AI response message
      const aiMessage: Message = {
        id: uuidv4(),
        content: finalResponse,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Add AI message to chat
      dispatch({ type: 'RECEIVE_MESSAGE', payload: aiMessage });

    } catch (error) {
      console.error('Error getting AI response:', error);

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Send error message (translate if Georgian was detected)
      let errorContent = "ვწუხვარ, ვერ შევძელი თქვენი მოთხოვნის დამუშავება. გთხოვთ, სცადოთ მოგვიანებით.";
      if (!isGeorgian) {
        errorContent = "I'm sorry, I couldn't process your request. Please try again later.";
      }

      const errorMessage: Message = {
        id: uuidv4(),
        content: errorContent,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
    }
  };

  // Send an image
  const sendImage = async (file: File) => {
    // Create a URL for the image
    const imageUrl = URL.createObjectURL(file);

    const newMessage: Message = {
      id: uuidv4(),
      content: '',
      sender: 'user',
      timestamp: Date.now(),
      status: 'sending',
      isRead: true,
      image: {
        url: imageUrl,
        name: file.name,
        type: file.type,
        size: file.size
      }
    };

    dispatch({ type: 'SEND_MESSAGE', payload: newMessage });

    // Update message status
    setTimeout(() => {
      dispatch({
        type: 'UPDATE_MESSAGE_STATUS',
        payload: { id: newMessage.id, status: 'sent' },
      });

      setTimeout(() => {
        dispatch({
          type: 'UPDATE_MESSAGE_STATUS',
          payload: { id: newMessage.id, status: 'delivered' },
        });
      }, 500);
    }, 300);

    // Show typing indicator
    dispatch({ type: 'SET_TYPING', payload: true });

    try {
      // Format chat history for the API
      const chatHistory = formatChatHistoryForUniversity(state.messages.slice(-10));

      // Create a message about the image for the AI
      const imagePrompt = `The user has shared an image named "${file.name}" (${(file.size / 1024).toFixed(1)} KB). Please acknowledge the image and offer assistance related to university services.`;

      // Get response from University AI
      const aiResponse = await getUniversityAIResponse(imagePrompt, chatHistory);

      // Create and send AI response message
      const aiMessage: Message = {
        id: uuidv4(),
        content: aiResponse,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Add AI message to chat
      dispatch({ type: 'RECEIVE_MESSAGE', payload: aiMessage });

    } catch (error) {
      console.error('Error getting AI response for image:', error);

      // Hide typing indicator
      dispatch({ type: 'SET_TYPING', payload: false });

      // Send error message
      const errorMessage: Message = {
        id: uuidv4(),
        content: "მივიღე თქვენი სურათი, მაგრამ ამჟამად მისი დამუშავება გამიჭირდება. როგორ შემიძლია სხვაგვარად დაგეხმაროთ?",
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: state.isOpen,
      };

      dispatch({ type: 'RECEIVE_MESSAGE', payload: errorMessage });
    }
  };

  // Toggle chat open/closed
  const toggleChat = () => {
    dispatch({ type: 'TOGGLE_CHAT' });
    if (!state.isOpen) {
      // When opening the chat, mark all as read
      dispatch({ type: 'MARK_ALL_READ' });
      saveLastReadTimestamp(Date.now());
    }
  };

  // Mark all messages as read
  const markAllAsRead = () => {
    dispatch({ type: 'MARK_ALL_READ' });
    saveLastReadTimestamp(Date.now());
  };

  // Set typing indicator
  const setTyping = (isTyping: boolean) => {
    dispatch({ type: 'SET_TYPING', payload: isTyping });
  };

  // Clear chat history
  const clearHistory = () => {
    dispatch({ type: 'CLEAR_HISTORY' });

    // Also clear messages from local storage
    if (config.enableHistory) {
      clearMessages();
    }

    // Add greeting message if configured
    if (config.theme?.greeting) {
      const greetingMessage: Message = {
        id: uuidv4(),
        content: config.theme.greeting,
        sender: 'agent',
        timestamp: Date.now(),
        status: 'delivered',
        isRead: true,
      };
      dispatch({ type: 'RECEIVE_MESSAGE', payload: greetingMessage });
    }
  };

  // Update configuration
  const updateConfig = (newConfig: Partial<ChatConfig>) => {
    setConfig((prevConfig) => ({
      ...prevConfig,
      ...newConfig,
      theme: {
        ...prevConfig.theme,
        ...(newConfig.theme || {}),
      },
    }));
  };

  // Set draft message
  const setDraftMessage = (message: string) => {
    dispatch({ type: 'SET_DRAFT_MESSAGE', payload: message });
  };

  return (
    <ChatContext.Provider
      value={{
        state,
        config,
        sendMessage,
        sendImage,
        toggleChat,
        markAllAsRead,
        setTyping,
        clearHistory,
        updateConfig,
        setDraftMessage,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

// Custom hook to use the chat context
export const useChat = (): ChatContextType => {
  const context = useContext(ChatContext);
  if (context === undefined) {
    throw new Error('useChat must be used within a ChatProvider');
  }
  return context;
};

export default ChatContext;
