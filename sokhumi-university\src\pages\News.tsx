import { Calendar, ArrowRight } from 'lucide-react';

const News = () => {
  return (
    <div className="news-page">
      <section className="page-header">
        <div className="container">
          <h1>სიახლეები / News</h1>
          <p>უნივერსიტეტის უახლესი სიახლეები</p>
          <p>Latest university news</p>
        </div>
      </section>

      <section className="section">
        <div className="container">
          <div className="news-grid grid grid-2">
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-15</span>
              </div>
              <h3>ახალი აკადემიური წლის დაწყება</h3>
              <h4>New Academic Year Begins</h4>
              <p>
                ჩვენ გვიხარია ახალი აკადემიური წლის დაწყება და ახალი სტუდენტების მიღება...
              </p>
              <p>
                We are excited to begin the new academic year and welcome new students...
              </p>
              <a href="#" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </a>
            </article>
            
            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-10</span>
              </div>
              <h3>კვლევითი კონფერენცია</h3>
              <h4>Research Conference</h4>
              <p>
                უნივერსიტეტში ჩატარდება საერთაშორისო კვლევითი კონფერენცია...
              </p>
              <p>
                The university will host an international research conference...
              </p>
              <a href="#" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </a>
            </article>

            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2025-01-05</span>
              </div>
              <h3>ახალი ლაბორატორია</h3>
              <h4>New Laboratory</h4>
              <p>
                გაიხსნა ახალი თანამედროვე კვლევითი ლაბორატორია...
              </p>
              <p>
                A new modern research laboratory has been opened...
              </p>
              <a href="#" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </a>
            </article>

            <article className="news-card card">
              <div className="news-meta">
                <Calendar size={16} />
                <span>2024-12-20</span>
              </div>
              <h3>სტუდენტური ღონისძიება</h3>
              <h4>Student Event</h4>
              <p>
                წარმატებით ჩატარდა სტუდენტური კულტურული ღონისძიება...
              </p>
              <p>
                A student cultural event was successfully held...
              </p>
              <a href="#" className="news-link">
                სრულად წაკითხვა / Read More <ArrowRight size={16} />
              </a>
            </article>
          </div>
        </div>
      </section>
    </div>
  );
};

export default News;
