/* University Message List Styles */
.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
  background-color: #f8fafc;
}

.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: rgba(30, 64, 175, 0.05);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: rgba(30, 64, 175, 0.2);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: rgba(30, 64, 175, 0.3);
}

.typing-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.agent-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
  overflow: hidden;
  flex-shrink: 0;
  border: 2px solid rgba(245, 158, 11, 0.3);
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
  color: white;
  font-weight: 600;
  font-size: 18px;
}

.typing-indicator {
  display: flex;
  flex-direction: column;
  background-color: white;
  padding: 12px 16px;
  border-radius: 16px;
  border-top-left-radius: 4px;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1);
  border: 1px solid rgba(30, 64, 175, 0.1);
}

.typing-text {
  font-size: 12px;
  color: #6b7280;
  opacity: 0.8;
  margin-bottom: 4px;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.typing-dot {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: #1e40af;
  border-radius: 50%;
  opacity: 0.6;
  animation: typing 1.5s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-5px);
    opacity: 1;
  }
  100% {
    transform: translateY(0);
    opacity: 0.6;
  }
}

@media (max-width: 768px) {
  .message-list {
    padding: 12px;
  }

  .agent-avatar {
    width: 30px;
    height: 30px;
    margin-right: 8px;
  }

  .typing-indicator {
    padding: 8px 12px;
    border-radius: 12px;
  }

  .typing-text {
    font-size: 11px;
  }

  .typing-dot {
    width: 6px;
    height: 6px;
  }
}

@media (max-width: 480px) {
  .message-list {
    padding: 8px;
  }
}
