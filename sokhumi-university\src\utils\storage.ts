import type { Message } from '../types/chat';

const STORAGE_KEY = 'university-chat-history';

/**
 * Save messages to local storage
 */
export const saveMessages = (messages: Message[]): void => {
  try {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(messages));
  } catch (error) {
    console.error('Error saving chat messages to local storage:', error);
  }
};

/**
 * Load messages from local storage
 */
export const loadMessages = (): Message[] => {
  try {
    const storedMessages = localStorage.getItem(STORAGE_KEY);
    return storedMessages ? JSON.parse(storedMessages) : [];
  } catch (error) {
    console.error('Error loading chat messages from local storage:', error);
    return [];
  }
};

/**
 * Clear all messages from local storage
 */
export const clearMessages = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
  } catch (error) {
    console.error('Error clearing chat messages from local storage:', error);
  }
};

/**
 * Save last read timestamp
 */
export const saveLastReadTimestamp = (timestamp: number): void => {
  try {
    localStorage.setItem(`${STORAGE_KEY}-last-read`, timestamp.toString());
  } catch (error) {
    console.error('Error saving last read timestamp to local storage:', error);
  }
};

/**
 * Load last read timestamp
 */
export const loadLastReadTimestamp = (): number | null => {
  try {
    const timestamp = localStorage.getItem(`${STORAGE_KEY}-last-read`);
    return timestamp ? parseInt(timestamp, 10) : null;
  } catch (error) {
    console.error('Error loading last read timestamp from local storage:', error);
    return null;
  }
};
