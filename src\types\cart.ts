export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  imageUrl: string;
  quantity: number;
  addedAt: number;
  category: string;
  description: string;
}

export interface CartState {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  isLoading: boolean;
  error: string | null;
  lastUpdated: number;
}

export interface CartActions {
  addItem: (productId: string, quantity?: number) => Promise<boolean>;
  removeItem: (productId: string) => Promise<boolean>;
  removeMultipleItems: (productIds: string[]) => Promise<boolean>;
  updateQuantity: (productId: string, quantity: number) => Promise<boolean>;
  clearCart: () => Promise<boolean>;
  getCartItem: (productId: string) => CartItem | undefined;
  isInCart: (productId: string) => boolean;
}

export interface CartContextType extends CartState, CartActions {}

export interface CartStorageData {
  items: CartItem[];
  lastUpdated: number;
  version: string;
}
