import React, { createContext, useContext, useState, useMemo } from 'react';
import type { ReactNode } from 'react';
import type { Product } from '../types/product';

// Define sorting options
export type SortCriteria = 'price' | 'rating' | 'name' | 'relevance';
export type SortOrder = 'asc' | 'desc';

// Define the context type
interface OverlayContextType {
  isOverlayVisible: boolean;
  recommendedProducts: Product[];
  recommendationTitle: string;
  sortCriteria: SortCriteria;
  sortOrder: SortOrder;
  showProductRecommendations: (products: Product[], title: string) => void;
  hideOverlay: () => void;
  setSorting: (criteria: SortCriteria, order: SortOrder) => void;
  sortedProducts: Product[]; // Computed sorted products
}

// Create the context with a default value
const OverlayContext = createContext<OverlayContextType | undefined>(undefined);

// Provider component
interface OverlayProviderProps {
  children: ReactNode;
}

export const OverlayProvider: React.FC<OverlayProviderProps> = ({ children }) => {
  const [isOverlayVisible, setIsOverlayVisible] = useState(false);
  const [recommendedProducts, setRecommendedProducts] = useState<Product[]>([]);
  const [recommendationTitle, setRecommendationTitle] = useState('Recommended Products');
  const [sortCriteria, setSortCriteria] = useState<SortCriteria>('relevance');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');

  // Show product recommendations in the overlay
  const showProductRecommendations = (products: Product[], title: string) => {
    setRecommendedProducts(products);
    setRecommendationTitle(title);
    setIsOverlayVisible(true);
  };

  // Hide the overlay
  const hideOverlay = () => {
    setIsOverlayVisible(false);
  };

  // Set sorting criteria and order
  const setSorting = (criteria: SortCriteria, order: SortOrder) => {
    setSortCriteria(criteria);
    setSortOrder(order);
  };

  // Compute sorted products based on current criteria and order
  const sortedProducts = useMemo(() => {
    if (!recommendedProducts.length) return [];

    const sorted = [...recommendedProducts];

    switch (sortCriteria) {
      case 'price':
        sorted.sort((a, b) => sortOrder === 'asc' ? a.price - b.price : b.price - a.price);
        break;
      case 'rating':
        sorted.sort((a, b) => sortOrder === 'asc' ? a.rating - b.rating : b.rating - a.rating);
        break;
      case 'name':
        sorted.sort((a, b) => {
          const nameA = a.name.toLowerCase();
          const nameB = b.name.toLowerCase();
          return sortOrder === 'asc'
            ? nameA.localeCompare(nameB)
            : nameB.localeCompare(nameA);
        });
        break;
      case 'relevance':
      default:
        // For relevance, we keep the original order as it's already sorted by relevance
        return recommendedProducts;
    }

    return sorted;
  }, [recommendedProducts, sortCriteria, sortOrder]);

  return (
    <OverlayContext.Provider
      value={{
        isOverlayVisible,
        recommendedProducts,
        recommendationTitle,
        sortCriteria,
        sortOrder,
        showProductRecommendations,
        hideOverlay,
        setSorting,
        sortedProducts,
      }}
    >
      {children}
    </OverlayContext.Provider>
  );
};

// Custom hook to use the overlay context
export const useOverlay = (): OverlayContextType => {
  const context = useContext(OverlayContext);
  if (context === undefined) {
    throw new Error('useOverlay must be used within an OverlayProvider');
  }
  return context;
};

export default OverlayContext;
