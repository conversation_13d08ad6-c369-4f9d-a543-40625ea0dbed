.message-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  scroll-behavior: smooth;
}

.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

.typing-container {
  display: flex;
  align-items: flex-start;
  margin-bottom: 16px;
}

.agent-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  margin-right: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.agent-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.agent-avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--chat-agent-message-bg);
  color: var(--chat-text-color);
  font-weight: 600;
}

.typing-indicator {
  display: flex;
  flex-direction: column;
  background-color: var(--chat-agent-message-bg);
  padding: 12px 16px;
  border-radius: var(--chat-message-border-radius);
  border-top-left-radius: 4px;
  width: fit-content;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.typing-text {
  font-size: 12px;
  color: var(--chat-text-color);
  opacity: 0.7;
  margin-bottom: 4px;
}

.typing-dots {
  display: flex;
  align-items: center;
}

.typing-dot {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background-color: var(--chat-text-color);
  border-radius: 50%;
  opacity: 0.6;
  animation: typing 1.5s infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .message-list {
    padding: 12px;
  }

  .agent-avatar {
    width: 30px;
    height: 30px;
    margin-right: 8px;
  }

  .typing-indicator {
    padding: 8px 12px;
  }

  .typing-text {
    font-size: 11px;
  }

  .typing-dot {
    width: 6px;
    height: 6px;
  }
}
