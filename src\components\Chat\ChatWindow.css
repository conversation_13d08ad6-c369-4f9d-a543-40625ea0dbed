.chat-window {
  position: fixed;
  bottom: 20px;
  width: 360px;
  height: 520px;
  border-radius: var(--chat-border-radius);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: var(--chat-shadow);
  animation: fadeIn 0.3s ease-out;
  z-index: 9999;
  max-height: calc(100vh - 40px);
}

@media (max-width: 768px) {
  .chat-window {
    width: calc(100% - 40px);
    height: 480px;
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .chat-window {
    width: calc(100% - 20px);
    height: 440px;
    bottom: 20px;
    right: 10px !important;
    left: 10px !important;
  }
}
