/* University Chat Header Styles */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);
}

.chat-header-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chat-header-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(245, 158, 11, 0.3);
}

.chat-header-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.chat-header-avatar-placeholder {
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.chat-header-title {
  display: flex;
  flex-direction: column;
}

.chat-header-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: white;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.chat-header-status {
  font-size: 12px;
  opacity: 0.8;
  color: #f59e0b;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

/* Header actions container */
.chat-header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Common styles for header buttons */
.chat-header-close,
.chat-header-clear {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.chat-header-close:hover,
.chat-header-clear:hover {
  background-color: rgba(245, 158, 11, 0.2);
  transform: scale(1.05);
}

.chat-header-close:active,
.chat-header-clear:active {
  transform: scale(0.95);
}

/* Confirmation dialog */
.chat-header-confirm {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: rgba(0, 0, 0, 0.2);
  padding: 4px 8px;
  border-radius: 16px;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.chat-header-confirm span {
  font-size: 12px;
  white-space: nowrap;
  color: white;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.chat-header-confirm-yes,
.chat-header-confirm-no {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  transition: background-color 0.2s;
  font-family: 'Noto Sans Georgian', 'Inter', sans-serif;
}

.chat-header-confirm-yes {
  background-color: rgba(245, 158, 11, 0.3);
}

.chat-header-confirm-yes:hover {
  background-color: rgba(245, 158, 11, 0.5);
}

.chat-header-confirm-no:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.chat-focus-visible:focus-visible {
  outline: 2px solid #f59e0b;
  outline-offset: 2px;
}

@media (max-width: 768px) {
  .chat-header {
    padding: 12px;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
  }

  .chat-header-avatar {
    width: 32px;
    height: 32px;
  }

  .chat-header-title h3 {
    font-size: 14px;
  }

  .chat-header-status {
    font-size: 10px;
  }

  .chat-header-confirm {
    padding: 2px 6px;
  }

  .chat-header-confirm span {
    font-size: 10px;
  }

  .chat-header-confirm-yes,
  .chat-header-confirm-no {
    padding: 2px 6px;
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }
}
