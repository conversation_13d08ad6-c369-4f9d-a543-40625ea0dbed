import { useState, type FC } from 'react';
import { useChat } from '../../context/ChatContext';
import './ChatHeader.css';

const ChatHeader: FC = () => {
  const { toggleChat, config, clearHistory } = useChat();
  const [showConfirm, setShowConfirm] = useState(false);

  // Handle clear history button click
  const handleClearClick = () => {
    setShowConfirm(true);
  };

  // Handle confirmation of clearing history
  const handleConfirmClear = () => {
    clearHistory(); // Clear chat messages
    setShowConfirm(false);
  };

  // Handle cancellation of clearing history
  const handleCancelClear = () => {
    setShowConfirm(false);
  };

  return (
    <div
      className="chat-header"
      style={{
        backgroundColor: config.theme?.primaryColor || '#1e40af',
        color: config.theme?.backgroundColor || '#ffffff'
      }}
    >
      <div className="chat-header-info">
        <div className="chat-header-avatar">
          {config.agentAvatar ? (
            <img src={config.agentAvatar} alt={config.agentName} />
          ) : (
            <div className="chat-header-avatar-placeholder">
              🎓
            </div>
          )}
        </div>
        <div className="chat-header-title">
          <h3>{config.agentName}</h3>
          <span className="chat-header-status">ონლაინ</span>
        </div>
      </div>

      <div className="chat-header-actions">
        {showConfirm ? (
          <div className="chat-header-confirm">
            <span>გასუფთავება?</span>
            <button
              className="chat-header-confirm-yes chat-focus-visible"
              onClick={handleConfirmClear}
              aria-label="Confirm clear history"
            >
              კი
            </button>
            <button
              className="chat-header-confirm-no chat-focus-visible"
              onClick={handleCancelClear}
              aria-label="Cancel clear history"
            >
              არა
            </button>
          </div>
        ) : (
          <button
            className="chat-header-clear chat-focus-visible"
            onClick={handleClearClick}
            aria-label="Clear chat history"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M2 4h12M5.333 4V2.667a1.333 1.333 0 011.334-1.334h2.666a1.333 1.333 0 011.334 1.334V4m2 0v9.333a1.333 1.333 0 01-1.334 1.334H4.667a1.333 1.333 0 01-1.334-1.334V4h9.334z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </button>
        )}

        <button
          className="chat-header-close chat-focus-visible"
          onClick={toggleChat}
          aria-label="Close chat"
        >
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12.5 3.5L3.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M3.5 3.5L12.5 12.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>
      </div>
    </div>
  );
};

export default ChatHeader;
