image: node:latest

cache:
  paths:
    - node_modules/

stages:
  - build
  - deploy

build:
  stage: build
  script:
    - npm ci
    - npm run build
  artifacts:
    paths:
      - dist/

pages:
  stage: deploy
  dependencies:
    - build
  script:
    - mkdir -p public
    - rm -rf public/*
    - cp -r dist/* public/
    - cp public/index.html public/404.html
  artifacts:
    paths:
      - public
  only:
    - master
