export type MessageStatus = 'sending' | 'sent' | 'delivered' | 'read';

export type MessageSender = 'user' | 'agent';

export interface Message {
  id: string;
  content: string;
  sender: MessageSender;
  timestamp: number;
  status: MessageStatus;
  isRead: boolean;
  image?: {
    url: string;
    name: string;
    type: string;
    size: number;
  };
}

export interface ChatState {
  isOpen: boolean;
  messages: Message[];
  unreadCount: number;
  isTyping: boolean;
  lastReadTimestamp: number | null;
  draftMessage: string;
}

export interface ChatTheme {
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  position: 'right' | 'left';
  initialState: 'minimized' | 'expanded';
  greeting?: string;
}

export interface ChatConfig {
  theme: ChatTheme;
  agentName: string;
  agentAvatar?: string;
  userAvatar?: string;
  enableSound: boolean;
  enableNotifications: boolean;
  enableAttachments: boolean;
  enableHistory: boolean;
}
