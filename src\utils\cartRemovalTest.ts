/**
 * Cart Addition & Removal Testing Utilities
 * These functions help debug and test cart addition and removal functionality
 */

import { allProducts } from '../data/products';

// Test function to debug cart removal issues
export const testCartRemoval = () => {
  console.log('🧪 Cart Removal Test Utilities');
  console.log('================================');
  
  // Get current cart items from localStorage
  const cartData = localStorage.getItem('cart-items');
  let cartItems = [];
  
  if (cartData) {
    try {
      cartItems = JSON.parse(cartData);
      console.log('📦 Current Cart Items:');
      cartItems.forEach((item: any, index: number) => {
        console.log(`${index + 1}. Product ID: ${item.productId} | Name: ${item.name} | Quantity: ${item.quantity}`);
      });
    } catch (error) {
      console.error('❌ Error parsing cart data:', error);
    }
  } else {
    console.log('📦 Cart is empty or not found in localStorage');
  }
  
  console.log('\n🔍 Available Products for Testing:');
  console.log('Smartphones (p121-p125):');
  allProducts.filter(p => p.id.startsWith('p12')).forEach(product => {
    console.log(`- ${product.id}: ${product.name}`);
  });
  
  console.log('\nOther Products:');
  allProducts.filter(p => !p.id.startsWith('p12')).slice(0, 5).forEach(product => {
    console.log(`- ${product.id}: ${product.name}`);
  });
  
  return {
    cartItems,
    availableProducts: allProducts.map(p => ({ id: p.id, name: p.name }))
  };
};

// Test function to simulate CHASTER addition commands
export const testAdditionCommand = (productIdentifier: string) => {
  console.log(`🤖 Testing CHASTER addition command for: "${productIdentifier}"`);

  console.log('🔍 Searching for product to add...');

  // Try exact product ID match
  let product = allProducts.find(p => p.id === productIdentifier);
  if (product) {
    console.log(`✅ Found by exact ID match: ${product.name} (${product.id})`);
    return { method: 'exact_id', product: product };
  }

  // Try product name match
  product = allProducts.find(p =>
    p.name.toLowerCase().includes(productIdentifier.toLowerCase()) ||
    productIdentifier.toLowerCase().includes(p.name.toLowerCase())
  );
  if (product) {
    console.log(`✅ Found by name match: ${product.name} (${product.id})`);
    return { method: 'name_match', product: product };
  }

  // Try partial ID match
  product = allProducts.find(p =>
    p.id.includes(productIdentifier) ||
    productIdentifier.includes(p.id)
  );
  if (product) {
    console.log(`✅ Found by partial ID match: ${product.name} (${product.id})`);
    return { method: 'partial_id', product: product };
  }

  console.log(`❌ No matching product found for: "${productIdentifier}"`);
  console.log('Available products (first 10):');
  allProducts.slice(0, 10).forEach(product => {
    console.log(`- ${product.id}: ${product.name}`);
  });

  return false;
};

// Test function to simulate CHASTER removal commands
export const testRemovalCommand = (productIdentifier: string) => {
  console.log(`🤖 Testing CHASTER removal command for: "${productIdentifier}"`);
  
  const cartData = localStorage.getItem('cart-items');
  let cartItems = [];
  
  if (cartData) {
    try {
      cartItems = JSON.parse(cartData);
    } catch (error) {
      console.error('❌ Error parsing cart data:', error);
      return false;
    }
  }
  
  console.log('🔍 Searching for product to remove...');
  
  // Try exact product ID match
  let cartItem = cartItems.find((item: any) => item.productId === productIdentifier);
  if (cartItem) {
    console.log(`✅ Found by exact ID match: ${cartItem.name} (${cartItem.productId})`);
    return { method: 'exact_id', item: cartItem };
  }
  
  // Try product name match
  const productByName = allProducts.find(p => 
    p.name.toLowerCase().includes(productIdentifier.toLowerCase()) ||
    productIdentifier.toLowerCase().includes(p.name.toLowerCase())
  );
  if (productByName) {
    cartItem = cartItems.find((item: any) => item.productId === productByName.id);
    if (cartItem) {
      console.log(`✅ Found by name match: ${cartItem.name} (${cartItem.productId})`);
      return { method: 'name_match', item: cartItem };
    }
  }
  
  // Try partial ID match
  cartItem = cartItems.find((item: any) => 
    item.productId.includes(productIdentifier) ||
    productIdentifier.includes(item.productId)
  );
  if (cartItem) {
    console.log(`✅ Found by partial ID match: ${cartItem.name} (${cartItem.productId})`);
    return { method: 'partial_id', item: cartItem };
  }
  
  console.log(`❌ No matching product found for: "${productIdentifier}"`);
  console.log('Available cart items:');
  cartItems.forEach((item: any) => {
    console.log(`- ${item.productId}: ${item.name}`);
  });
  
  return false;
};

// Test function to validate addition commands
export const validateAdditionCommands = () => {
  console.log('🧪 Testing CHASTER Addition Command Patterns');
  console.log('==========================================');

  const testCommands = [
    '[ADD_TO_CART: p121]',
    '[ADD TO CART: p122]',
    '[CART_ADD: p123]',
    'I\'ll add that to your cart [ADD_TO_CART: p124]',
    'Added! [ADD_TO_CART: UltraMax Pro]'
  ];

  const addToCartRegexes = [
    /\[ADD_TO_CART:\s*([^\]]+)\]/i,
    /\[ADD TO CART:\s*([^\]]+)\]/i,
    /\[CART_ADD:\s*([^\]]+)\]/i
  ];

  testCommands.forEach(command => {
    console.log(`\n🧪 Testing: "${command}"`);

    let found = false;
    for (const regex of addToCartRegexes) {
      const match = command.match(regex);
      if (match && match[1]) {
        console.log(`✅ Matched with regex: ${regex.source}`);
        console.log(`📦 Extracted product ID: "${match[1].trim()}"`);
        found = true;
        break;
      }
    }

    if (!found) {
      console.log('❌ No regex match found');
    }
  });
};

// Test function to validate removal commands
export const validateRemovalCommands = () => {
  console.log('🧪 Testing CHASTER Removal Command Patterns');
  console.log('==========================================');

  const testCommands = [
    '[REMOVE_FROM_CART: p121]',
    '[REMOVE FROM CART: p122]',
    '[CART_REMOVE: p123]',
    'I\'ll remove that from your cart [REMOVE_FROM_CART: p124]',
    'Removed! [REMOVE_FROM_CART: UltraMax Pro]'
  ];

  const removeFromCartRegexes = [
    /\[REMOVE_FROM_CART:\s*([^\]]+)\]/i,
    /\[REMOVE FROM CART:\s*([^\]]+)\]/i,
    /\[CART_REMOVE:\s*([^\]]+)\]/i
  ];

  testCommands.forEach(command => {
    console.log(`\n🧪 Testing: "${command}"`);

    let found = false;
    for (const regex of removeFromCartRegexes) {
      const match = command.match(regex);
      if (match && match[1]) {
        console.log(`✅ Matched with regex: ${regex.source}`);
        console.log(`📦 Extracted product ID: "${match[1].trim()}"`);
        found = true;
        break;
      }
    }

    if (!found) {
      console.log('❌ No regex match found');
    }
  });
};

// Function to clear cart for testing
export const clearCartForTesting = () => {
  localStorage.removeItem('cart-items');
  console.log('🧹 Cart cleared for testing');
};

// Function to add test items to cart
export const addTestItemsToCart = () => {
  const testItems = [
    {
      id: 'test-1',
      productId: 'p121',
      name: 'UltraMax Pro Flagship Smartphone',
      price: 999.99,
      quantity: 1,
      addedAt: Date.now(),
      category: 'Electronics',
      description: 'Test smartphone'
    },
    {
      id: 'test-2', 
      productId: 'p122',
      name: 'TechNova Elite Smartphone',
      price: 899.99,
      quantity: 2,
      addedAt: Date.now(),
      category: 'Electronics',
      description: 'Test smartphone 2'
    }
  ];
  
  localStorage.setItem('cart-items', JSON.stringify(testItems));
  console.log('🛒 Added test items to cart:');
  testItems.forEach(item => {
    console.log(`- ${item.productId}: ${item.name} (${item.quantity}x)`);
  });
};

// Main test function for both addition and removal
export const runCartTests = () => {
  console.log('🚀 Running Complete Cart Addition & Removal Tests');
  console.log('=================================================');

  // Test 1: Current cart state
  console.log('\n📋 Test 1: Current Cart State');
  testCartRemoval();

  // Test 2: Addition command validation
  console.log('\n📋 Test 2: Addition Command Pattern Validation');
  validateAdditionCommands();

  // Test 3: Removal command validation
  console.log('\n📋 Test 3: Removal Command Pattern Validation');
  validateRemovalCommands();

  // Test 4: Product addition tests
  console.log('\n📋 Test 4: Product Addition Tests');

  console.log('\n🧪 Testing addition by exact ID:');
  testAdditionCommand('p121');

  console.log('\n🧪 Testing addition by product name:');
  testAdditionCommand('UltraMax Pro');

  console.log('\n🧪 Testing addition by partial match:');
  testAdditionCommand('smartphone');

  console.log('\n🧪 Testing addition with invalid identifier:');
  testAdditionCommand('nonexistent');

  // Test 5: Add test items and test removal
  console.log('\n📋 Test 5: Test Item Removal');
  addTestItemsToCart();

  console.log('\n🧪 Testing removal by exact ID:');
  testRemovalCommand('p121');

  console.log('\n🧪 Testing removal by product name:');
  testRemovalCommand('TechNova Elite');

  console.log('\n🧪 Testing removal by partial match:');
  testRemovalCommand('p12');

  console.log('\n🧪 Testing removal with invalid identifier:');
  testRemovalCommand('nonexistent');

  console.log('\n✅ Complete cart tests completed!');
  console.log('Check the console output above for detailed results.');
};

// Legacy function for backward compatibility
export const runCartRemovalTests = () => {
  console.log('🚀 Running Cart Removal Tests (Legacy)');
  console.log('======================================');

  // Test 1: Current cart state
  console.log('\n📋 Test 1: Current Cart State');
  testCartRemoval();

  // Test 2: Command validation
  console.log('\n📋 Test 2: Command Pattern Validation');
  validateRemovalCommands();

  // Test 3: Add test items and test removal
  console.log('\n📋 Test 3: Test Item Removal');
  addTestItemsToCart();

  console.log('\n🧪 Testing removal by exact ID:');
  testRemovalCommand('p121');

  console.log('\n🧪 Testing removal by product name:');
  testRemovalCommand('TechNova Elite');

  console.log('\n🧪 Testing removal by partial match:');
  testRemovalCommand('p12');

  console.log('\n🧪 Testing removal with invalid identifier:');
  testRemovalCommand('nonexistent');

  console.log('\n✅ Cart removal tests completed!');
  console.log('Check the console output above for detailed results.');
};

// Export for global access
if (typeof window !== 'undefined') {
  (window as any).testCartRemoval = testCartRemoval;
  (window as any).testAdditionCommand = testAdditionCommand;
  (window as any).testRemovalCommand = testRemovalCommand;
  (window as any).validateAdditionCommands = validateAdditionCommands;
  (window as any).validateRemovalCommands = validateRemovalCommands;
  (window as any).runCartTests = runCartTests;
  (window as any).runCartRemovalTests = runCartRemovalTests;
  (window as any).clearCartForTesting = clearCartForTesting;
  (window as any).addTestItemsToCart = addTestItemsToCart;
}
