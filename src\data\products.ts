import type { Product } from '../types/product';
import { productsBatch1 } from './products-batch1';
import { productsBatch2 } from './products-batch2';
import { productsBatch3 } from './products-batch3';
import { productsBatch4 } from './products-batch4';
import { productsBatch5 } from './products-batch5';
import { productsBatch6 } from './products-batch6';
import { productsBatch7 } from './products-batch7';
import { productsBatch8 } from './products-batch8';
import { productsBatch9 } from './products-batch9';
import { productsBatch10 } from './products-batch10';
import { productsBatch11 } from './products-batch11';
import { productsBatch12 } from './products-batch12';
import { productsBatch13 } from './products-batch13';
import { productsBatch14 } from './products-batch14';
import { productsBatch15 } from './products-batch15';

// Combine all product batches
export const allProducts: Product[] = [
  ...productsBatch1,
  ...productsBatch2,
  ...productsBatch3,
  ...productsBatch4,
  ...productsBatch5,
  ...productsBatch6,
  ...productsBatch7,
  ...productsBatch8,
  ...productsBatch9,
  ...productsBatch10,
  ...productsBatch11,
  ...productsBatch12,
  ...productsBatch13,
  ...productsBatch14,
  ...productsBatch15
];

// Function to get products by category
export const getProductsByCategory = (category: string): Product[] => {
  return allProducts.filter(product => product.category === category);
};

// Function to get products by tag
export const getProductsByTag = (tag: string): Product[] => {
  if (!tag || tag.trim() === '') {
    return [];
  }

  const normalizedTag = tag.toLowerCase().trim();
  return allProducts.filter(product =>
    product.tags.some(t => t.toLowerCase() === normalizedTag || t.toLowerCase().includes(normalizedTag))
  );
};

// Function to get products by product type (more specific than category)
export const getProductsByType = (productType: string): Product[] => {
  if (!productType || productType.trim() === '') {
    return [];
  }

  const type = productType.toLowerCase().trim();

  // Product type mapping to standardize common terms
  const typeMapping: Record<string, string[]> = {
    'phone': ['phone', 'smartphone', 'mobile', 'cell phone'],
    'laptop': ['laptop', 'notebook', 'computer', 'gaming laptop'],
    'headphone': ['headphone', 'headset', 'earphone', 'earbud'],
    'speaker': ['speaker', 'bluetooth speaker', 'wireless speaker'],
    'camera': ['camera', 'digital camera', 'mirrorless', 'dslr'],
    'watch': ['watch', 'smartwatch', 'wristwatch'],
    'tablet': ['tablet', 'ipad'],
    'tv': ['tv', 'television', 'smart tv'],
    'monitor': ['monitor', 'display', 'screen'],
    'furniture': ['chair', 'table', 'desk', 'sofa', 'bed', 'furniture'],
    'kitchen': ['cookware', 'knife', 'pot', 'pan', 'blender', 'coffee maker'],
    'audio': ['speaker', 'headphone', 'earphone', 'earbud', 'microphone']
  };

  // Find all related terms for the given product type
  let searchTerms: string[] = [type];

  for (const [mappedType, terms] of Object.entries(typeMapping)) {
    if (mappedType === type || terms.includes(type)) {
      searchTerms = [...searchTerms, ...terms];
      break;
    }
  }

  // Search for products matching any of the search terms
  return allProducts.filter(product => {
    const productName = product.name.toLowerCase();
    const productDesc = product.description.toLowerCase();
    const productTags = product.tags.map(t => t.toLowerCase());

    // Check if product name, description or tags contain any of the search terms
    return searchTerms.some(term =>
      productName.includes(term) ||
      productDesc.includes(term) ||
      productTags.some(tag => tag === term || tag.includes(term))
    );
  });
};

// Function to get featured products (highest rated)
export const getFeaturedProducts = (count: number = 4): Product[] => {
  return [...allProducts]
    .sort((a, b) => b.rating - a.rating)
    .slice(0, count);
};

// Function to search products with improved relevance
export const searchProducts = (query: string): Product[] => {
  if (!query || query.trim() === '') {
    return [];
  }

  const lowercaseQuery = query.toLowerCase().trim();
  const queryTerms = lowercaseQuery.split(/\s+/).filter(term => term.length > 2);

  // If no valid search terms, return empty array
  if (queryTerms.length === 0) {
    return [];
  }

  // Score-based relevance ranking
  const scoredProducts = allProducts.map(product => {
    let score = 0;
    const productName = product.name.toLowerCase();
    const productDesc = product.description.toLowerCase();
    const productCategory = product.category.toLowerCase();
    const productTags = product.tags.map(tag => tag.toLowerCase());

    // Check for exact matches in name (highest priority)
    if (productName === lowercaseQuery) {
      score += 100;
    } else if (productName.includes(lowercaseQuery)) {
      score += 50;
    }

    // Check for matches in individual terms
    for (const term of queryTerms) {
      // Name matches (high priority)
      if (productName.includes(term)) {
        score += 20;

        // Bonus for term at the beginning of name
        if (productName.startsWith(term)) {
          score += 10;
        }
      }

      // Category matches (medium-high priority)
      if (productCategory.includes(term)) {
        score += 15;
      }

      // Tag matches (medium priority)
      if (productTags.some(tag => tag.includes(term) || tag === term)) {
        score += 15;

        // Exact tag match bonus
        if (productTags.some(tag => tag === term)) {
          score += 5;
        }
      }

      // Description matches (lower priority)
      if (productDesc.includes(term)) {
        score += 5;
      }
    }

    return { product, score };
  });

  // Filter out products with zero score and sort by score (descending)
  return scoredProducts
    .filter(item => item.score > 0)
    .sort((a, b) => b.score - a.score)
    .map(item => item.product);
};
