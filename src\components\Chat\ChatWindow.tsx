import { useEffect } from 'react';
import type { FC } from 'react';
import { useChat } from '../../context/ChatContext';
import ChatHeader from './ChatHeader';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import './ChatWindow.css';

const ChatWindow: FC = () => {
  const { state, config, markAllAsRead } = useChat();

  // Mark messages as read when chat window is opened
  useEffect(() => {
    if (state.isOpen && state.unreadCount > 0) {
      markAllAsRead();
    }
  }, [state.isOpen, state.unreadCount, markAllAsRead]);

  if (!state.isOpen) {
    return null;
  }

  return (
    <div
      className="chat-window"
      style={{
        [config.theme.position]: '20px',
        backgroundColor: config.theme.backgroundColor,
        color: config.theme.textColor
      }}
      role="dialog"
      aria-modal="true"
      aria-labelledby="chat-header"
    >
      <ChatHeader />
      <MessageList />
      <ChatInput />
    </div>
  );
};

export default ChatWindow;
