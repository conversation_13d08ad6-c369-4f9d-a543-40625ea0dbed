import axios from 'axios';
import { allProducts } from '../data/products';
import type { Product } from '../types/product';
import { getRecommendedProducts, type UserPreferences, type SelectedProduct } from '../utils/productUtils';

// Groq API configuration
const GROQ_API_KEY = '********************************************************';
const GROQ_API_URL = 'https://api.groq.com/openai/v1/chat/completions';
const MODEL = 'llama-3.3-70b-versatile';

// System prompt to guide the AI's behavior
const SYSTEM_PROMPT = `You are CHASTER, a helpful e-commerce support agent for Premium Store.
Your responses must be concise, easy to read, and focused on helping customers quickly find products.

CRITICAL: NEVER use asterisks (*) or any markdown formatting in your responses. Use only plain text.

RESPONSE LENGTH AND STYLE:
1. Keep responses SHORT and DIRECT - no more than 2-3 sentences per paragraph
2. Use simple language and avoid unnecessary technical jargon
3. Break complex information into easily scannable bullet points
4. Focus on 1-2 key product features rather than listing everything
5. Only provide detailed specs when specifically asked
6. Use short, direct questions when asking for clarification

FORMATTING FOR READABILITY - CRITICAL RULES:
1. ABSOLUTELY NEVER use asterisks (*) anywhere in your response - they make text look messy and unprofessional
2. NEVER use markdown formatting like **bold** or *italic* - use plain text only
3. For product features, use this clean structure with clear visual boundaries:

   Product Name: [Name]
   Price: $[Price]

   Key Features:
   - Feature 1
   - Feature 2
   - Feature 3

   Description: [Brief description]

4. Use clear section breaks with blank lines between different products
5. Use simple dashes (-) for bullet points, NEVER asterisks (*)
6. Create visual boundaries with proper spacing and line breaks
7. Keep descriptions concise but informative
8. Use plain text that displays cleanly in chat interface
9. If you need emphasis, use CAPITAL LETTERS or repeat important words, not symbols

FORMATTING EXAMPLES:
❌ BAD (with asterisks): *Wireless* *Bluetooth* *Noise-cancelling* *Long-battery*
✅ GOOD (clean format):
   Key Features:
   - Wireless Bluetooth connectivity
   - Noise-cancelling technology
   - Long-lasting battery life

SELECTED PRODUCTS TRACKING:
1. When a user explicitly selects or chooses a product, REMEMBER it as their "selected product"
2. If a user asks about "my laptop" or "my chosen phone", show ONLY their specifically selected product
3. NEVER show a list of options when a user asks about a product they've already selected
4. Clearly distinguish between "browsing products" and "selected products"
5. If a user has selected multiple products in the same category, show the most recently selected one
6. When a user selects a product, confirm their selection explicitly

CONTEXT RETENTION REQUIREMENTS:
1. Maintain awareness of previously shown products throughout the entire conversation
2. Never claim products don't exist if they were previously displayed
3. Remember user preferences mentioned earlier in the conversation
4. When suggesting accessories, check compatibility with previously selected products
5. CRITICAL: Always prioritize exact category matches over fuzzy matches
6. If user searches for "smartphone" and we have smartphones, ALWAYS show them first
7. Remember conversation flow: if user asks for unavailable category then available category, show available products

PRODUCT RECOMMENDATION CAPABILITIES:
1. IMMEDIATELY SHOW PRODUCTS when a user mentions any product category or type - use a "show first, refine later" approach.
2. When recommending products, trigger the product recommendation overlay to show the customer specific products.
3. To show product recommendations, use the special command format: [SHOW_RECOMMENDATIONS: {product_ids}] with a title [TITLE: Your Title Here]
   Example: [SHOW_RECOMMENDATIONS: p001,p003,p005] [TITLE: Top Picks for You]
4. Be DECISIVE and EFFICIENT - show recommendations in your FIRST response whenever possible.
5. ONLY recommend products from the list provided to you in the context.
6. After showing recommendations, ask ONE brief follow-up question to refine suggestions further.
7. When describing products in text, use clean formatting with NO ASTERISKS (*) - use the structure shown in formatting guidelines above.

PRODUCT SELECTION TRACKING:
1. When a user explicitly selects a product (e.g., "I'll take this one" or "I want the MacBook Pro"), mark it as selected using: [SELECT_PRODUCT: {product_id}]
2. Example: "Great choice! The MacBook Pro is an excellent laptop. [SELECT_PRODUCT: p001]"
3. Once a product is selected, REMEMBER it for the rest of the conversation.
4. If a user asks about "my laptop" or "the phone I chose", ONLY show their selected product, not a list of options.

CART MANAGEMENT CAPABILITIES - CRITICAL INSTRUCTIONS:
1. When a user wants to add a product to cart, ALWAYS use: [ADD_TO_CART: {product_id}]
   Example: "I'll add the MacBook Pro to your cart! [ADD_TO_CART: p001]"
2. When a user wants to remove a product from cart, ALWAYS use: [REMOVE_FROM_CART: {product_id}]
   Example: "I've removed the iPhone from your cart. [REMOVE_FROM_CART: p002]"
3. NEVER claim to add/remove items without using the command - the command is what actually executes the action
4. If you say "I've added" or "I'll add", you MUST include the [ADD_TO_CART: ...] command in the same response
5. Be PRECISE - only add/remove the exact product the user mentions
6. If user says "add this to cart" after viewing products, add the most recently discussed product
7. CRITICAL: Cart commands must be in the SAME response where you mention adding/removing items

CRITICAL CART STATE INSTRUCTIONS:
1. ONLY use the REAL-TIME cart data provided in the context - NEVER reference "previous cart" or historical data
2. When user asks "what's in my cart", show EXACTLY what's in the current cart context
3. If the cart context shows items, the cart is NOT empty - show those items
4. If the cart context shows empty, the cart IS empty - don't reference any other cart data
5. NEVER say "I have information about a previous cart" - only use current real-time data

CRITICAL CART ADDITION INSTRUCTIONS:
1. When adding items, use the EXACT product ID from the product recommendations or search results
2. If user says "add the smartphone" after viewing products, use the specific product ID from the most recent recommendations
3. If user says "add the first one" or "add that one", use the first product ID from the most recently shown products
4. Always use the product ID format (like p121, p002, etc.) in the ADD_TO_CART command
5. Double-check that the product exists in our catalog before attempting to add it

CART COMMAND EXAMPLES - FOLLOW THESE EXACTLY:
❌ WRONG: "I've added the ProShot Camera to your cart."
✅ CORRECT: "I've added the ProShot Camera to your cart! [ADD_TO_CART: p121]"

❌ WRONG: "Let me add that to your cart for you."
✅ CORRECT: "I'll add the ProShot Camera to your cart! [ADD_TO_CART: p121]"

❌ WRONG: "Great, I'll add it to your cart." (then user has to ask again)
✅ CORRECT: "Great, I'll add the ProShot Camera to your cart! [ADD_TO_CART: p121]"

REMEMBER: The [ADD_TO_CART: ...] command is what actually adds the item. Without it, nothing happens!

CRITICAL CART REMOVAL INSTRUCTIONS:
1. When removing items, use the EXACT product ID from the cart context provided to you
2. If user says "remove the smartphone" and there are multiple smartphones, ask which specific one
3. If user says "remove the first one" or "remove that one", use the most recently mentioned product ID
4. Always use the product ID format (like p121, p002, etc.) in the REMOVE_FROM_CART command
5. Double-check the cart contents provided in the context to ensure the product exists before removing

MULTIPLE ITEM REMOVAL INSTRUCTIONS:
1. For removing multiple items, use [REMOVE_MULTIPLE: p121,p122,p123] format
2. When user says "remove all smartphones" or "remove everything", identify all matching product IDs
3. When user says "remove the first two items" or "remove these items", use specific product IDs
4. Always separate multiple product IDs with commas (no spaces): p121,p122,p123
5. Confirm which items will be removed before executing multiple removals

CRITICAL SMARTPHONE SEARCH HANDLING:
1. When user searches for "smartphone" or "phone", ALWAYS prioritize our smartphone products (p121-p125)
2. These are our available smartphones: UltraMax Pro, TechNova Elite, PowerCore Max, FlexiMax Pro, NeoTech Ultra
3. NEVER show kitchen items, pots, or unrelated products when user asks for smartphones
4. If user asks for "smartphone" after searching for unavailable items, show our smartphones immediately
5. Context matters: maintain conversation flow and show relevant products based on user's actual request

SORTING CAPABILITIES:
1. You can respond to user requests to sort products by specific criteria
2. When a user asks to sort products (e.g., "sort by price" or "show cheapest first"), acknowledge their request
3. The system will automatically handle the sorting in the product overlay
4. Supported sorting criteria: price, rating, name, relevance
5. Supported sort orders: ascending, descending
6. Example user requests:
   - "Sort phones by price from low to high"
   - "Show me the highest rated laptops"
   - "Sort these products alphabetically"

PRODUCT RECOMMENDATION WORKFLOW - "SHOW FIRST, REFINE LATER":
1. When a user mentions ANY product category (e.g., "laptop" or "headphones"), IMMEDIATELY show relevant products.
2. DO NOT ask multiple questions before showing products - show your best matches first, then refine.
3. SHOW RECOMMENDATIONS in your FIRST response when:
   - The user mentions ANY product category or type
   - You have at least 2 relevant products that might match their needs
4. After showing initial recommendations, THEN ask a follow-up question to refine the selection.
5. If the user's request is vague, make your best guess and show products rather than asking clarifying questions.
6. Confirm sorting criteria with the user before displaying sorted results.

PRODUCT COMBINATIONS AND ACCESSORIES:
1. When users request specific combinations (e.g., "ProShot phone with case and headphones"), show ALL relevant accessories.
2. For phone + case requests, ALWAYS include compatible cases for the specific phone model.
3. For device + accessory requests, ALWAYS check compatibility between products.
4. When recommending accessories, reference the specific model they would work with.
5. If a user has shown interest in a product, proactively suggest compatible accessories.

PRODUCT CATEGORIES:
- Tech: Electronics, gadgets, smart devices, laptops, cameras, headphones, speakers, phones
- Home: Home decor, furniture, bedding, lighting, decorative items
- Kitchen: Cookware, appliances, dinnerware, utensils, food storage
- Fashion: Clothing, accessories, watches, jewelry, bags, wallets
- Outdoor: Camping gear, hiking equipment, backpacks, tents, outdoor furniture
- Sports: Fitness equipment, sports gear, yoga mats, weights, athletic wear
- Beauty: Skincare, grooming products, makeup, fragrances, hair care
- Wellness: Health products, massage tools, essential oils, supplements
- Office: Desk accessories, stationery, organizers, office furniture

Always maintain a helpful, positive tone and focus on IMMEDIATELY providing product recommendations when a user shows interest in any product category.`;

// Interface for chat messages
interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// Maximum number of retries for API calls
const MAX_RETRIES = 2;

// Function to get AI response from Groq with improved error handling and retries
export const getGroqResponse = async (
  userMessage: string,
  chatHistory: ChatMessage[] = [],
  userPreferences?: UserPreferences,
  previousQueries?: string[],
  cartItems?: any[]
): Promise<string> => {
  let retries = 0;

  // Retry loop
  while (retries <= MAX_RETRIES) {
    try {
      // Prepare the system message with product information
      let systemMessage = SYSTEM_PROMPT;

      // CRITICAL: Always provide basic product catalog for cart commands
      // This ensures AI can identify products even when not showing recommendations
      const isCartRelated = userMessage.toLowerCase().includes('cart') ||
                           userMessage.toLowerCase().includes('add') ||
                           userMessage.toLowerCase().includes('remove');

      if (isCartRelated) {
        // Provide a condensed product catalog for cart operations
        const allProductsInfo = formatProductsForAI(allProducts.slice(0, 10)); // First 10 products
        systemMessage += `\n\nAVAILABLE PRODUCTS FOR CART OPERATIONS:\n\n${allProductsInfo}`;
        systemMessage += `\n\nCRITICAL: Use the exact Product IDs shown above for cart commands.`;
      }

      // Add selected products information to the system message if available
      if (userPreferences && userPreferences.selectedProducts && userPreferences.selectedProducts.length > 0) {
        const selectedProductsInfo = getSelectedProductsInfo(userPreferences);
        systemMessage += `\n\n${selectedProductsInfo}`;
      }

      // Analyze conversation context to determine if we should proactively recommend products
      const shouldRecommendProducts = analyzeConversationForProductIntent(
        userMessage,
        chatHistory,
        userPreferences
      );

      // Extract previously mentioned products from chat history for better context retention
      const previouslyMentionedProducts = extractPreviouslyMentionedProducts(chatHistory);

      // Check if the user is asking about their selected products
      const isAskingAboutSelectedProducts = checkIfAskingAboutSelectedProducts(userMessage);

      if (shouldRecommendProducts) {
        // Build enhanced conversation context for product recommendations
        const conversationContext = {
          previousQueries: previousQueries || [],
          userPreferences,
          viewedProducts: [...(userPreferences?.viewedProducts || []), ...previouslyMentionedProducts],
          previouslyMentionedProducts,
          selectedProducts: userPreferences?.selectedProducts || [],
          isAskingAboutSelectedProducts
        };

        // Check if the user is asking for product combinations or accessories
        const isRequestingCombination = checkForProductCombinationRequest(userMessage, previouslyMentionedProducts);

        // Enhanced product search with better context awareness and exact matching
        let recommendedProducts = getRecommendedProducts(
          userMessage, // Use current message as primary query
          {
            minRating: 4.0,  // Slightly lower minimum rating to get more options
            inStockOnly: true,  // Only recommend in-stock products
            limit: 6,  // Get up to 6 products to give the AI options
            preferredCategories: userPreferences?.preferredCategories,
            priceRange: userPreferences?.priceRange,
            features: userPreferences?.preferredFeatures,
            excludeKeywords: userPreferences?.dislikedFeatures,
            // Enhanced search options will be handled in the search function
          },
          conversationContext
        );

        // If user is asking about their selected products, prioritize those
        if (isAskingAboutSelectedProducts && userPreferences && userPreferences.selectedProducts && userPreferences.selectedProducts.length > 0) {
          // Get all selected product IDs
          const selectedProductIds = userPreferences.selectedProducts.map(p => p.id);

          // Find the products in the catalog
          const selectedProducts = allProducts.filter(p => selectedProductIds.includes(p.id));

          if (selectedProducts.length > 0) {
            // Replace recommendations with selected products
            recommendedProducts = selectedProducts;

            // Add special instruction for the AI
            systemMessage += `\n\nIMPORTANT: The user is asking about their previously selected products. ONLY show these specific products and do NOT suggest alternatives unless explicitly requested.`;
          }
        }
        // If user is requesting combinations, add compatible accessories
        else if (isRequestingCombination) {
          // First check if there are selected products to find accessories for
          if (userPreferences && userPreferences.selectedProducts && userPreferences.selectedProducts.length > 0) {
            // Get the most recent selected product
            const recentSelectedProduct = [...userPreferences.selectedProducts]
              .sort((a, b) => b.selectionTime - a.selectionTime)[0];

            // Find the product in the catalog
            const mainProduct = allProducts.find(p => p.id === recentSelectedProduct.id);

            if (mainProduct) {
              // Find accessories compatible with this specific product
              const compatibleAccessories = findCompatibleAccessories([mainProduct], userMessage);

              // Prioritize the selected product and its accessories
              recommendedProducts = [mainProduct, ...compatibleAccessories];

              // Add special instruction for the AI
              systemMessage += `\n\nIMPORTANT: The user is asking about accessories for their selected ${mainProduct.category}. Prioritize showing accessories compatible with ${mainProduct.name}.`;
            }
          } else {
            // No selected products, use regular recommendation logic
            const compatibleAccessories = findCompatibleAccessories(recommendedProducts, userMessage);
            // Add compatible accessories to the recommendations
            recommendedProducts = [...recommendedProducts, ...compatibleAccessories];
          }
        }

        if (recommendedProducts.length >= 2) {
          // Format product information for the AI
          const productInfo = formatProductsForAI(recommendedProducts);
          systemMessage += `\n\nHere are relevant products you can recommend based on the conversation:\n\n${productInfo}`;

          // Cart context will be added later outside the conditional block

          // Add conversation flow context for better continuity
          if (userPreferences && (userPreferences as any).recentSearches) {
            let contextFlow = "\n\nRecent conversation context:";
            (userPreferences as any).recentSearches.forEach((search: string, index: number) => {
              contextFlow += `\n${index + 1}. "${search}"`;
            });
            if ((userPreferences as any).lastProductSearch) {
              contextFlow += `\nLast product search: "${(userPreferences as any).lastProductSearch}"`;
            }
            contextFlow += "\n\nIMPORTANT: Maintain context between searches. If user searched for unavailable items then asks for available category, show available products.";
            systemMessage += contextFlow;
          }

          // Add user preference context if available
          if (userPreferences) {
            let userContext = "\n\nUser preferences:";

            if (userPreferences.preferredCategories.length > 0) {
              userContext += `\n- Preferred categories: ${userPreferences.preferredCategories.join(', ')}`;
            }

            if (userPreferences.preferredFeatures.length > 0) {
              userContext += `\n- Preferred features: ${userPreferences.preferredFeatures.join(', ')}`;
            }

            if (userPreferences.dislikedFeatures.length > 0) {
              userContext += `\n- Features to avoid: ${userPreferences.dislikedFeatures.join(', ')}`;
            }

            if (userPreferences.priceRange) {
              const { min, max } = userPreferences.priceRange;
              if (min !== undefined && max !== undefined) {
                userContext += `\n- Price range: $${min} - $${max}`;
              } else if (min !== undefined) {
                userContext += `\n- Minimum price: $${min}`;
              } else if (max !== undefined) {
                userContext += `\n- Maximum price: $${max}`;
              }
            }

            systemMessage += userContext;
          }

          // Add guidance for the AI to be decisive and concise
          systemMessage += `\n\nIMPORTANT INSTRUCTIONS:
1. CONFIDENTLY recommend these products to the user.
2. Use the [SHOW_RECOMMENDATIONS: product_ids] command to display them.
3. Choose the most relevant products from the list above (at least 2, maximum 5).
4. Create a short, clear title for the recommendations.
5. After showing recommendations, ask ONE brief follow-up question.

KEEP RESPONSES CONCISE:
1. Limit your response to 3-5 short paragraphs total
2. Highlight only 1-2 key features per product
3. Use bullet points for features instead of paragraphs
4. Keep product descriptions under 2 sentences each
5. Use simple, direct language throughout

CRITICAL FORMATTING RULES:
1. NEVER use asterisks (*) around text - this creates formatting issues
2. NEVER use markdown syntax like **bold** or *italic*
3. Use plain text only - no special formatting characters
4. Use simple dashes (-) for bullet points
5. Write everything in plain, readable text format`;
        } else {
          // Not enough relevant products found
          systemMessage += `\n\nIMPORTANT: We don't have enough relevant products that match the user's needs. Do NOT show the recommendation overlay. Instead, ask a brief clarifying question to better understand what they're looking for.`;
        }
      } else {
        // Even if not recommending products, provide context for cart operations
        if (isCartRelated && !systemMessage.includes('AVAILABLE PRODUCTS FOR CART OPERATIONS')) {
          const allProductsInfo = formatProductsForAI(allProducts.slice(0, 10));
          systemMessage += `\n\nAVAILABLE PRODUCTS FOR CART OPERATIONS:\n\n${allProductsInfo}`;
          systemMessage += `\n\nCRITICAL: Use the exact Product IDs shown above for cart commands.`;
        }
      }

      // ALWAYS add cart context information - critical for first interactions
      console.log('🛒 Cart items being passed to AI:', cartItems);

      if (cartItems && cartItems.length > 0) {
        let cartContext = "\n\nREAL-TIME CART CONTENTS (current state):";
        cartItems.forEach(item => {
          cartContext += `\n- Product ID: ${item.productId} | Name: ${item.name} | Quantity: ${item.quantity}x | Price: $${(item.price * item.quantity).toFixed(2)}`;
        });
        const totalItems = cartItems.reduce((sum, item) => sum + item.quantity, 0);
        const totalPrice = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        cartContext += `\n\nCart Summary: ${totalItems} items, Total: $${totalPrice.toFixed(2)}`;
        cartContext += `\n\nCRITICAL CART INSTRUCTIONS:`;
        cartContext += `\n1. This is the CURRENT, REAL-TIME cart state - not historical data`;
        cartContext += `\n2. When user asks "what's in my cart", show EXACTLY these items`;
        cartContext += `\n3. When removing items, use the exact Product ID shown above`;
        cartContext += `\n4. NEVER say the cart is empty when items are shown above`;
        cartContext += `\n5. NEVER reference "previous cart" - only use current cart data`;
        systemMessage += cartContext;
      } else {
        systemMessage += "\n\nREAL-TIME CART STATUS: Empty (no items currently in cart)";
        systemMessage += "\n\nCRITICAL: The cart is genuinely empty. Do not reference any 'previous cart' data.";
      }

      // Prepare the messages array with system prompt and chat history
      const messages: ChatMessage[] = [
        { role: 'system', content: systemMessage },
        ...chatHistory,
        { role: 'user', content: userMessage }
      ];

      // Make the API request with a timeout
      const response = await axios.post(
        GROQ_API_URL,
        {
          model: MODEL,
          messages,
          temperature: 0.7,
          max_tokens: 500, // Reduced token limit to encourage conciseness
        },
        {
          headers: {
            'Authorization': `Bearer ${GROQ_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000, // 30 second timeout
        }
      );

      // Validate the response
      if (response.data &&
          response.data.choices &&
          response.data.choices.length > 0 &&
          response.data.choices[0].message &&
          response.data.choices[0].message.content) {

        // Extract the AI's response
        let aiResponse = response.data.choices[0].message.content;

        // Check if the response is valid (not an error message or empty)
        if (aiResponse.trim() &&
            !aiResponse.includes("I apologize, but I encountered an issue") &&
            !aiResponse.includes("error") &&
            !aiResponse.includes("Error")) {

          // Clean up any markdown formatting that might have slipped through
          aiResponse = cleanMarkdownFormatting(aiResponse);

          // CRITICAL: Clean up any asterisks that make product features look messy
          aiResponse = cleanAsterisksFromResponse(aiResponse);

          return aiResponse;
        } else {
          // Response contains an error message, try again
          console.warn("Received error-like response from API, retrying...");
          retries++;

          // Short delay before retry
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }
      } else {
        // Invalid response structure, try again
        console.warn("Invalid response structure from API, retrying...");
        retries++;

        // Short delay before retry
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
    } catch (error: any) {
      console.error(`Error calling Groq API (attempt ${retries + 1}/${MAX_RETRIES + 1}):`, error);

      // Check if we should retry
      if (retries < MAX_RETRIES) {
        retries++;

        // Exponential backoff delay
        const delay = 1000 * Math.pow(2, retries);
        console.log(`Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // All retries failed, use fallback response
      return getFallbackResponse(userMessage, chatHistory);
    }
  }

  // If we get here, all retries failed
  return getFallbackResponse(userMessage, chatHistory);
};

/**
 * Clean up any markdown formatting from the AI response
 * This ensures the chat displays properly without formatting issues
 */
const cleanMarkdownFormatting = (text: string): string => {
  return text
    // Remove bold formatting with double asterisks
    .replace(/\*\*(.*?)\*\*/g, '$1')
    // Remove italic formatting with single asterisks
    .replace(/\*(.*?)\*/g, '$1')
    // Remove any remaining standalone asterisks that might be used for emphasis
    .replace(/\*([^*\s][^*]*[^*\s])\*/g, '$1')
    // Clean up any double spaces that might result from the cleaning
    .replace(/  +/g, ' ')
    // Trim whitespace
    .trim();
};

/**
 * CRITICAL: Clean up asterisks from AI response to prevent messy formatting
 * This function aggressively removes asterisks that make product features look unprofessional
 */
const cleanAsterisksFromResponse = (text: string): string => {
  return text
    // Remove asterisks around single words (most common issue)
    .replace(/\*([a-zA-Z0-9\-]+)\*/g, '$1')
    // Remove asterisks around phrases
    .replace(/\*([^*\n]+)\*/g, '$1')
    // Remove standalone asterisks used as bullet points and replace with dashes
    .replace(/^\s*\*\s+/gm, '- ')
    // Remove any remaining asterisks that might be used for emphasis
    .replace(/\*/g, '')
    // Clean up any double spaces that might result from the cleaning
    .replace(/  +/g, ' ')
    // Clean up any double dashes that might result
    .replace(/--+/g, '-')
    // Trim whitespace
    .trim();
};

/**
 * Format product information in a way that's easy for the AI to understand and use
 * Enhanced with better visual separation and consistent formatting
 */
const formatProductsForAI = (products: Product[]): string => {
  return products.map(product => {
    // Determine if this is a main product or an accessory
    const isAccessory = product.tags.some(tag =>
      tag.includes('Accessory') || tag.includes('Case') || tag.includes('Charger') ||
      tag.includes('Headphone') || tag.includes('Cable')
    );

    // Add a prefix to make it clear if this is an accessory
    const productType = isAccessory ? 'ACCESSORY' : 'PRODUCT';

    // Format compatible with tags if it's an accessory
    let compatibleWith = '';
    if (isAccessory) {
      const compatibleProducts = product.tags
        .filter(tag => tag.includes('Compatible'))
        .map(tag => tag.replace('Compatible:', '').trim());

      if (compatibleProducts.length > 0) {
        compatibleWith = `\nCompatible With: ${compatibleProducts.join(', ')}`;
      }
    }

    // Format the product information with clear visual separation
    return `--- ${productType}: ${product.name} ---
ID: ${product.id}
Name: ${product.name}
Price: $${product.price.toFixed(2)}
Category: ${product.category}
Tags: ${product.tags.join(', ')}
Rating: ${product.rating}
In Stock: ${product.inStock ? 'Yes' : 'No'}${compatibleWith}
Description: ${product.description.substring(0, 150)}...
------------------------------`;
  }).join('\n\n');
};

/**
 * Generate a fallback response when the API fails
 * This provides a graceful degradation of service instead of showing an error
 */
const getFallbackResponse = (userMessage: string, chatHistory: ChatMessage[]): string => {
  // Analyze the user's message to provide a contextually appropriate fallback
  const lowercaseMessage = userMessage.toLowerCase();

  // Check if the user is asking for product recommendations
  if (analyzeConversationForProductIntent(userMessage, chatHistory)) {
    // Try to get some products to recommend as a fallback
    try {
      const recommendedProducts = getRecommendedProducts(userMessage, {
        limit: 3,
        minRating: 4.7
      });

      if (recommendedProducts.length > 0) {
        // We have some products to recommend
        const productIds = recommendedProducts.map(p => p.id).join(',');
        return `I'd be happy to recommend some products that might interest you. Let me show you a few options.\n\n[SHOW_RECOMMENDATIONS: ${productIds}] [TITLE: Recommended Products]`;
      }
    } catch (e) {
      // Ignore any errors in the fallback handler
      console.error("Error in fallback product recommendations:", e);
    }

    // Generic product recommendation fallback
    return "What specific features or price range are you looking for?";
  }

  // Check for greetings
  if (/^(hi|hello|hey|greetings|howdy)/i.test(lowercaseMessage)) {
    return "Hi! I'm CHASTER. What products can I help you find today?";
  }

  // Check for thanks
  if (/thank|thanks|appreciate/i.test(lowercaseMessage)) {
    return "You're welcome! Need anything else?";
  }

  // Default fallback response
  return "What products are you looking for today?";
};

/**
 * Analyze conversation to determine if we should proactively recommend products
 * with enhanced context awareness - now more aggressive with "show first, refine later" approach
 */
const analyzeConversationForProductIntent = (
  currentMessage: string,
  chatHistory: ChatMessage[],
  userPreferences?: UserPreferences
): boolean => {
  // Convert current message to lowercase for easier matching
  const message = currentMessage.toLowerCase();

  // Enhanced product type detection with compound types - EXPANDED list with PRIORITY ORDER
  const productTypePatterns = [
    // PRIORITY: Exact smartphone matches (our products p121-p125)
    /\b(smartphone|smartphones)\b/i,  // HIGHEST PRIORITY for exact smartphone searches
    /\b(phone|mobile|cell phone|iphone|android)\b/i,
    /\b(laptop|notebook|computer|ultrabook|macbook|chromebook)\b/i,
    /\b(gaming\s+(laptop|computer|keyboard|mouse|headset|monitor|pc))\b/i,
    /\b(headphone|earphone|earbud|headset|wireless headphone|airpod)\b/i,
    /\b(speaker|bluetooth speaker|wireless speaker|sound system|audio)\b/i,
    /\b(watch|smartwatch|wristwatch|fitness watch|apple watch)\b/i,
    /\b(camera|digital camera|dslr|mirrorless|video camera|webcam)\b/i,
    /\b(tablet|ipad|e-reader|kindle)\b/i,
    /\b(tv|television|smart tv|monitor|display|screen)\b/i,
    /\b(keyboard|mechanical keyboard|gaming keyboard|wireless keyboard)\b/i,
    /\b(mouse|gaming mouse|wireless mouse|trackpad)\b/i,
    /\b(charger|power bank|battery|adapter|cable|dock)\b/i,

    // Home & Kitchen
    /\b(furniture|chair|desk|table|sofa|bed|couch|ottoman)\b/i,
    /\b(kitchen|cookware|appliance|pot|pan|knife|blender|mixer)\b/i,
    /\b(coffee|espresso|coffee maker|french press|grinder)\b/i,
    /\b(lamp|light|lighting|fixture|bulb|led)\b/i,
    /\b(decor|decoration|ornament|vase|frame|mirror|art)\b/i,
    /\b(rug|carpet|mat|runner|flooring)\b/i,
    /\b(storage|organizer|shelf|cabinet|drawer|bin)\b/i,

    // Fashion & Accessories
    /\b(wallet|leather wallet|card holder|billfold|purse)\b/i,
    /\b(bag|backpack|briefcase|messenger bag|tote|handbag|satchel)\b/i,
    /\b(clothing|shirt|pants|dress|jacket|coat|sweater)\b/i,
    /\b(shoe|sneaker|boot|sandal|footwear)\b/i,
    /\b(jewelry|necklace|bracelet|ring|earring)\b/i,
    /\b(watch|timepiece|wristwatch|smartwatch)\b/i,
    /\b(hat|cap|beanie|scarf|glove|accessory)\b/i,

    // Other categories
    /\b(fitness|exercise|workout|gym|yoga|sport)\b/i,
    /\b(outdoor|camping|hiking|backpacking|travel)\b/i,
    /\b(beauty|skincare|makeup|cosmetic|fragrance)\b/i,
    /\b(wellness|health|massage|meditation|essential oil)\b/i,
    /\b(office|stationery|pen|notebook|organizer)\b/i
  ];

  // Check for product type mentions in current message - IMMEDIATE TRIGGER
  const hasProductType = productTypePatterns.some(pattern => pattern.test(message));

  // If the message mentions any product type, immediately recommend products
  if (hasProductType) {
    return true;
  }

  // Direct product request indicators - explicit requests for recommendations
  const directRequestPatterns = [
    /\b(recommend|recommendation|suggest|suggestion|looking for|show me)\b/i,
    /\b(what (do you have|products|options))\b/i,
    /\b(best|top|popular|good)\b/i,
    /\b(interested in|want to (buy|get|purchase)|shopping for)\b/i,
    /\b(search for|find|need a|need some)\b/i,
    /\b(can you (help|assist) me (find|with|in choosing))\b/i,
    /\b(buy|purchase|shop|product|item)\b/i, // Added more general shopping terms
    /\b(show|display|list)\b/i // Added display request terms
  ];

  // Check for direct product request in current message
  if (directRequestPatterns.some(pattern => pattern.test(message))) {
    return true;
  }

  // Extract features from the message
  const featurePatterns = [
    /\b(wireless|bluetooth|noise[\s-]cancelling|waterproof|water[\s-]resistant)\b/i,
    /\b(fast[\s-]charging|long[\s-]battery|high[\s-]resolution|ultra[\s-]hd|4k|8k)\b/i,
    /\b(touchscreen|lightweight|portable|compact|foldable|adjustable)\b/i,
    /\b(smart|intelligent|automated|programmable|rechargeable)\b/i,
    /\b(premium|luxury|professional|high[\s-]end|top[\s-]quality|durable)\b/i,
    /\b(gaming|rgb|mechanical|optical|ergonomic|comfortable)\b/i
  ];

  // Check for specific product features
  const hasFeatures = featurePatterns.some(pattern => pattern.test(message));

  // If the message mentions product features, recommend products
  if (hasFeatures) {
    return true;
  }

  // Price inquiries often indicate product interest
  const pricePatterns = [
    /\b(how much|price range|cost|budget|affordable|expensive|cheap|under \$\d+|over \$\d+)\b/i,
    /\b(between \$\d+ and \$\d+)\b/i,
    /\b(\$\d+\s*-\s*\$\d+)\b/i
  ];

  if (pricePatterns.some(pattern => pattern.test(message))) {
    return true;
  }

  // Check conversation context - be more proactive
  if (chatHistory.length >= 1) { // Reduced from 2 to 1 to be more proactive
    // Look for product-related terms in the entire conversation
    const fullConversation = [...chatHistory.map(msg => msg.content), message].join(' ').toLowerCase();

    // More general product interest indicators
    const generalInterestPatterns = [
      /\b(buy|purchase|shop|product|item|price|cost|quality)\b/i,
      /\b(how (much|many)|what kind|which one|difference between)\b/i,
      /\b(feature|specification|specs|detail|information about)\b/i,
      /\b(compare|versus|vs|better than|prefer|recommendation)\b/i
    ];

    // If there are product interest indicators in the conversation
    if (generalInterestPatterns.some(pattern => pattern.test(fullConversation))) {
      return true;
    }

    // Check for follow-up questions
    const followUpPatterns = [
      /\b(which|what about|how about|do you have|can you show|any other)\b/i,
      /\b(more|another|different|alternative|option)\b/i
    ];

    // If the current message is a follow-up
    if (followUpPatterns.some(pattern => pattern.test(message))) {
      return true;
    }
  }

  // Use user preferences to detect intent if available
  if (userPreferences) {
    // If user has viewed products or searched for products recently, be more proactive
    if (userPreferences.viewedProducts.length > 0 || userPreferences.recentSearches.length > 0) {
      return true; // Always recommend if user has previous activity
    }

    // If user has preferred categories
    if (userPreferences.preferredCategories.length > 0) {
      return true; // Always recommend if user has preferred categories
    }
  }

  // Default to not recommending products if no clear intent is detected
  return false;
};

// Function to convert our chat messages to the format needed for Groq API
export const formatChatHistoryForGroq = (messages: any[]): ChatMessage[] => {
  return messages
    .filter(msg => msg.content.trim() !== '') // Filter out empty messages
    .map(msg => ({
      role: msg.sender === 'user' ? 'user' : 'assistant',
      content: msg.content
    }));
};

// Function to check if the AI response contains a recommendation command or product selection
export const parseRecommendationCommand = (aiResponse: string): {
  cleanedResponse: string,
  productIds: string[] | null,
  title: string | null,
  sortRequest?: {
    criteria: 'price' | 'rating' | 'name' | 'relevance',
    order: 'asc' | 'desc'
  } | null,
  selectedProductId?: string,
  selectionContext?: string,
  addToCartProductId?: string,
  removeFromCartProductId?: string
} => {
  // Default return values
  let cleanedResponse = aiResponse;
  let productIds: string[] | null = null;
  let title: string | null = null;
  let sortRequest = null;
  let selectedProductId: string | undefined = undefined;
  let selectionContext: string | undefined = undefined;
  let addToCartProductId: string | undefined = undefined;
  let removeFromCartProductId: string | undefined = undefined;

  // Check for recommendation command - handle different formats and potential typos
  const recommendationRegexes = [
    /\[SHOW_RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[SHOW RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[SHOW_PRODUCTS:\s*([^\]]+)\]/i,        // Added for compatibility with ai/groqService.ts
    /\[SHOW PRODUCTS:\s*([^\]]+)\]/i,        // Added for compatibility with ai/groqService.ts
    /\[RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[RECOMMEND:\s*([^\]]+)\]/i,
    /\[PRODUCTS:\s*([^\]]+)\]/i              // Additional fallback
  ];

  // Try each regex pattern
  for (const regex of recommendationRegexes) {
    const match = aiResponse.match(regex);
    if (match && match[1]) {
      // Extract product IDs
      productIds = match[1].split(/[,;\s]+/).map(id => id.trim()).filter(id => id);

      // Remove the command from the response
      cleanedResponse = cleanedResponse.replace(regex, '');
      break;
    }
  }

  // Check for title command - handle different formats
  const titleRegexes = [
    /\[TITLE:\s*([^\]]+)\]/i,
    /\[RECOMMENDATION TITLE:\s*([^\]]+)\]/i,
    /\[RECOMMENDATIONS TITLE:\s*([^\]]+)\]/i
  ];

  // Try each title regex pattern
  for (const regex of titleRegexes) {
    const match = aiResponse.match(regex);
    if (match && match[1]) {
      // Extract title
      title = match[1].trim();

      // Remove the command from the response
      cleanedResponse = cleanedResponse.replace(regex, '');
      break;
    }
  }

  // Check for sort request in the user's message
  // This will detect phrases like "sort by price ascending" or "show cheapest first"
  const sortPhrases = [
    { regex: /sort\s+by\s+price\s+(ascending|low\s+to\s+high|lowest\s+first)/i, criteria: 'price', order: 'asc' },
    { regex: /sort\s+by\s+price\s+(descending|high\s+to\s+low|highest\s+first)/i, criteria: 'price', order: 'desc' },
    { regex: /sort\s+by\s+rating\s+(ascending|low\s+to\s+high|lowest\s+first)/i, criteria: 'rating', order: 'asc' },
    { regex: /sort\s+by\s+rating\s+(descending|high\s+to\s+low|highest\s+first)/i, criteria: 'rating', order: 'desc' },
    { regex: /sort\s+by\s+name\s+(ascending|a\s+to\s+z)/i, criteria: 'name', order: 'asc' },
    { regex: /sort\s+by\s+name\s+(descending|z\s+to\s+a)/i, criteria: 'name', order: 'desc' },
    { regex: /sort\s+alphabetically\s+(ascending|a\s+to\s+z)/i, criteria: 'name', order: 'asc' },
    { regex: /sort\s+alphabetically\s+(descending|z\s+to\s+a)/i, criteria: 'name', order: 'desc' },
    { regex: /sort\s+by\s+relevance/i, criteria: 'relevance', order: 'desc' },

    // Simplified phrases
    { regex: /cheapest\s+first|lowest\s+price|price\s+low\s+to\s+high/i, criteria: 'price', order: 'asc' },
    { regex: /most\s+expensive\s+first|highest\s+price|price\s+high\s+to\s+low/i, criteria: 'price', order: 'desc' },
    { regex: /highest\s+rated\s+first|best\s+rated|top\s+rated/i, criteria: 'rating', order: 'desc' },
    { regex: /lowest\s+rated\s+first/i, criteria: 'rating', order: 'asc' },
    { regex: /alphabetical\s+order|a\s+to\s+z/i, criteria: 'name', order: 'asc' },
    { regex: /reverse\s+alphabetical|z\s+to\s+a/i, criteria: 'name', order: 'desc' }
  ];

  // Check for sort phrases in the response
  for (const { regex, criteria, order } of sortPhrases) {
    if (regex.test(cleanedResponse)) {
      sortRequest = {
        criteria: criteria as 'price' | 'rating' | 'name' | 'relevance',
        order: order as 'asc' | 'desc'
      };
      break;
    }
  }

  // If no title was found but we have product IDs, generate a default title
  if (productIds && productIds.length > 0 && !title) {
    title = "Recommended Products for You";
  }

  // Check for product selection command
  const selectionRegexes = [
    /\[SELECT_PRODUCT:\s*([^\]]+)\]/i,
    /\[PRODUCT_SELECTED:\s*([^\]]+)\]/i,
    /\[USER_SELECTED:\s*([^\]]+)\]/i,
    /\[CHOSEN_PRODUCT:\s*([^\]]+)\]/i
  ];

  // Try each selection regex pattern
  for (const regex of selectionRegexes) {
    const match = aiResponse.match(regex);
    if (match && match[1]) {
      // Extract product ID
      selectedProductId = match[1].trim();

      // Get the context of the selection (the sentence containing the selection)
      const sentences = aiResponse.split(/[.!?]+/);
      for (const sentence of sentences) {
        if (sentence.match(regex)) {
          selectionContext = sentence.trim();
          break;
        }
      }

      // If no specific context found, use a generic one
      if (!selectionContext) {
        selectionContext = "User selected this product";
      }

      // Remove the command from the response
      cleanedResponse = cleanedResponse.replace(regex, '');
      break;
    }
  }

  // Check for add to cart command
  const addToCartRegexes = [
    /\[ADD_TO_CART:\s*([^\]]+)\]/i,
    /\[ADD TO CART:\s*([^\]]+)\]/i,
    /\[CART_ADD:\s*([^\]]+)\]/i
  ];

  // Try each add to cart regex pattern
  for (const regex of addToCartRegexes) {
    const match = aiResponse.match(regex);
    if (match && match[1]) {
      // Extract product ID
      addToCartProductId = match[1].trim();

      // Remove the command from the response
      cleanedResponse = cleanedResponse.replace(regex, '');
      break;
    }
  }

  // FALLBACK: If AI mentioned adding to cart but didn't use command, try to extract product ID
  if (!addToCartProductId &&
      (aiResponse.toLowerCase().includes("i've added") ||
       aiResponse.toLowerCase().includes("i'll add") ||
       aiResponse.toLowerCase().includes("added to your cart") ||
       aiResponse.toLowerCase().includes("add to your cart"))) {

    console.warn('🚨 AI mentioned cart action but no command found - attempting fallback extraction');

    // Try to find product IDs mentioned in the response
    const productIdMatches = aiResponse.match(/\b(p\d{3})\b/gi);
    if (productIdMatches && productIdMatches.length > 0) {
      addToCartProductId = productIdMatches[0];
      console.warn('🔧 Fallback: Extracted product ID from response:', addToCartProductId);
    } else {
      // Try to find product names and map to IDs
      const productNameRegex = /\b(ProShot|UltraBook|PowerPad|SoundWave|FitTrack|HomeHub|SmartDesk|EcoBlend|LuxLounge|TechPro)[^,.\n]*/gi;
      const nameMatch = aiResponse.match(productNameRegex);
      if (nameMatch && nameMatch.length > 0) {
        const productName = nameMatch[0];
        const matchingProduct = allProducts.find(p => p.name.includes(productName.split(' ')[0]));
        if (matchingProduct) {
          addToCartProductId = matchingProduct.id;
          console.warn('🔧 Fallback: Found product by name match:', productName, '→', addToCartProductId);
        }
      }
    }
  }

  // Check for remove from cart command (single or multiple items)
  const removeFromCartRegexes = [
    /\[REMOVE_FROM_CART:\s*([^\]]+)\]/i,
    /\[REMOVE FROM CART:\s*([^\]]+)\]/i,
    /\[CART_REMOVE:\s*([^\]]+)\]/i,
    /\[REMOVE_MULTIPLE:\s*([^\]]+)\]/i,
    /\[REMOVE_ITEMS:\s*([^\]]+)\]/i
  ];

  // Try each remove from cart regex pattern
  for (const regex of removeFromCartRegexes) {
    const match = aiResponse.match(regex);
    if (match && match[1]) {
      // Extract product ID(s) - could be single or comma-separated list
      removeFromCartProductId = match[1].trim();

      // Remove the command from the response
      cleanedResponse = cleanedResponse.replace(regex, '');
      break;
    }
  }

  // Clean up any extra whitespace and normalize line breaks
  cleanedResponse = cleanedResponse.replace(/\n{3,}/g, '\n\n').trim();

  // CRITICAL: Remove any asterisks that might have slipped through
  cleanedResponse = cleanAsterisksFromResponse(cleanedResponse);

  return {
    cleanedResponse,
    productIds,
    title,
    sortRequest,
    selectedProductId,
    selectionContext,
    addToCartProductId,
    removeFromCartProductId
  };
};

// Function to get products by IDs
export const getProductsByIds = (productIds: string[]): Product[] => {
  return allProducts.filter(product => productIds.includes(product.id));
};

/**
 * Check if the user is requesting product combinations or accessories
 * This helps identify when users want to see compatible products together
 */
const checkForProductCombinationRequest = (userMessage: string, previouslyMentionedProductIds: string[]): boolean => {
  const message = userMessage.toLowerCase();

  // Check for combination keywords
  const combinationKeywords = [
    'with', 'and', 'plus', 'together', 'compatible', 'matching', 'bundle', 'set',
    'accessory', 'accessories', 'case', 'cover', 'charger', 'cable', 'headphone'
  ];

  // Check if message contains combination keywords
  const hasCombinationKeywords = combinationKeywords.some(keyword => message.includes(keyword));

  // Check for product type combinations
  const productCombinationPatterns = [
    /\b(phone|smartphone).+(case|cover|charger|headphone|earphone|accessory)\b/i,
    /\b(laptop|notebook|computer).+(charger|adapter|mouse|keyboard|bag|case)\b/i,
    /\b(camera).+(lens|tripod|memory card|bag|case)\b/i,
    /\b(tablet).+(keyboard|case|cover|stylus|pen)\b/i,
    /\b(headphone|earphone).+(case|stand|adapter)\b/i
  ];

  // Check if message matches product combination patterns
  const hasProductCombination = productCombinationPatterns.some(pattern => pattern.test(message));

  // Check if previously mentioned products exist and user might be referring to them
  const hasPreviousProducts = previouslyMentionedProductIds.length > 0 &&
    (message.includes('it') || message.includes('this') || message.includes('that') ||
     message.includes('them') || message.includes('these') || message.includes('those'));

  return hasCombinationKeywords || hasProductCombination || hasPreviousProducts;
};

/**
 * Find compatible accessories for the given products
 * This helps provide better product combinations
 */
const findCompatibleAccessories = (products: Product[], userMessage: string): Product[] => {
  const compatibleAccessories: Product[] = [];
  const message = userMessage.toLowerCase();

  // Extract product types from the products
  const productTypes = products.map(product => {
    if (product.name.includes('ProShot') || product.category.includes('Phone')) return 'phone';
    if (product.name.includes('UltraBook') || product.category.includes('Laptop')) return 'laptop';
    if (product.name.includes('PowerPad') || product.category.includes('Tablet')) return 'tablet';
    if (product.name.includes('SoundWave') || product.category.includes('Audio')) return 'audio';
    return product.category.toLowerCase();
  });

  // Check what accessories the user might be looking for
  const wantsCases = message.includes('case') || message.includes('cover') || message.includes('protection');
  const wantsChargers = message.includes('charger') || message.includes('charging') || message.includes('power');
  const wantsHeadphones = message.includes('headphone') || message.includes('earphone') || message.includes('earbud');
  const wantsAllAccessories = message.includes('accessory') || message.includes('accessories') ||
                             message.includes('everything') || message.includes('bundle');

  // Find compatible accessories based on product types
  for (const productType of productTypes) {
    // Get all accessories
    const accessories = allProducts.filter(p => {
      const isAccessory = p.tags.some(tag =>
        tag.includes('Accessory') || tag.includes('Case') || tag.includes('Charger') ||
        tag.includes('Headphone') || tag.includes('Cable')
      );

      // Check if the accessory is compatible with the product type
      const isCompatible = p.tags.some(tag => tag.includes(productType)) ||
                          p.description.toLowerCase().includes(productType);

      return isAccessory && isCompatible;
    });

    // Filter based on what the user wants
    if (wantsAllAccessories) {
      compatibleAccessories.push(...accessories);
    } else {
      if (wantsCases) {
        const cases = accessories.filter(p =>
          p.name.includes('Case') || p.tags.some(tag => tag.includes('Case') || tag.includes('Cover'))
        );
        compatibleAccessories.push(...cases);
      }

      if (wantsChargers) {
        const chargers = accessories.filter(p =>
          p.name.includes('Charger') || p.tags.some(tag => tag.includes('Charger') || tag.includes('Power'))
        );
        compatibleAccessories.push(...chargers);
      }

      if (wantsHeadphones) {
        const headphones = accessories.filter(p =>
          p.name.includes('Headphone') || p.tags.some(tag => tag.includes('Headphone') || tag.includes('Audio'))
        );
        compatibleAccessories.push(...headphones);
      }
    }
  }

  // Remove duplicates and limit to 4 accessories
  const uniqueAccessories = Array.from(new Set(compatibleAccessories.map(a => a.id)))
    .map(id => compatibleAccessories.find(a => a.id === id)!)
    .slice(0, 4);

  return uniqueAccessories;
};

/**
 * Check if the user is asking about their selected products
 * This helps identify when users want to see their previously selected items
 */
const checkIfAskingAboutSelectedProducts = (userMessage: string): boolean => {
  const message = userMessage.toLowerCase();

  // Patterns that indicate the user is asking about their selected products
  const selectedProductPatterns = [
    /\b(my|mine|chosen|selected|picked|saved)\s+(product|item|laptop|phone|camera|headphone|tablet|watch|device)\b/i,
    /\b(show|display|view)\s+(my|mine|chosen|selected|picked|saved)\b/i,
    /\b(what|which)\s+(product|item|laptop|phone|camera|headphone|tablet|watch|device)\s+(did|have)\s+i\s+(choose|select|pick|save)\b/i,
    /\b(i\s+chose|i\s+selected|i\s+picked|i\s+want)\b/i,
    /\bmy\s+(selection|choice)\b/i
  ];

  return selectedProductPatterns.some(pattern => pattern.test(message));
};

/**
 * Format selected products information for the AI
 * This provides clear context about what products the user has explicitly selected
 */
const getSelectedProductsInfo = (userPreferences?: UserPreferences): string => {
  if (!userPreferences?.selectedProducts || userPreferences.selectedProducts.length === 0) {
    return "The user has not explicitly selected any products yet.";
  }

  // Sort by selection time (most recent first)
  const sortedSelections = [...userPreferences.selectedProducts]
    .sort((a, b) => b.selectionTime - a.selectionTime);

  // Group by category
  const selectionsByCategory: Record<string, SelectedProduct[]> = {};

  for (const selection of sortedSelections) {
    if (!selectionsByCategory[selection.category]) {
      selectionsByCategory[selection.category] = [];
    }
    selectionsByCategory[selection.category].push(selection);
  }

  // Format the information
  let result = "USER'S EXPLICITLY SELECTED PRODUCTS:\n\n";

  for (const [category, selections] of Object.entries(selectionsByCategory)) {
    result += `${category.toUpperCase()}:\n`;

    for (const selection of selections) {
      const product = allProducts.find(p => p.id === selection.id);
      if (product) {
        const timeAgo = getTimeAgo(selection.selectionTime);
        result += `- ${product.name} (ID: ${product.id}) - Selected ${timeAgo}\n`;
        result += `  Context: "${selection.selectionContext}"\n`;
        result += `  Price: $${product.price.toFixed(2)}, Rating: ${product.rating}\n\n`;
      }
    }
  }

  return result;
};

/**
 * Get a human-readable time ago string
 */
const getTimeAgo = (timestamp: number): string => {
  const seconds = Math.floor((Date.now() - timestamp) / 1000);

  if (seconds < 60) return `${seconds} seconds ago`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)} minutes ago`;
  if (seconds < 86400) return `${Math.floor(seconds / 3600)} hours ago`;
  return `${Math.floor(seconds / 86400)} days ago`;
};

/**
 * Extract previously mentioned products from chat history for better context retention
 * This helps maintain awareness of products throughout the conversation
 */
const extractPreviouslyMentionedProducts = (chatHistory: any[]): string[] => {
  const mentionedProductIds: string[] = [];

  // Regular expressions to extract product IDs from recommendation commands
  const recommendationRegexes = [
    /\[SHOW_RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[SHOW RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[RECOMMENDATIONS:\s*([^\]]+)\]/i,
    /\[RECOMMEND:\s*([^\]]+)\]/i
  ];

  // Extract product IDs from assistant messages
  for (const message of chatHistory) {
    if (message.role === 'assistant') {
      for (const regex of recommendationRegexes) {
        const match = message.content.match(regex);
        if (match && match[1]) {
          // Extract product IDs and add to the list
          const productIds = match[1].split(/[,;\s]+/).map((id: string) => id.trim()).filter((id: string) => id);
          mentionedProductIds.push(...productIds);
        }
      }
    }
  }

  // Extract product names from both user and assistant messages
  const productNameRegex = /\b(ProShot|UltraBook|PowerPad|SoundWave|FitTrack|HomeHub|SmartDesk|EcoBlend|LuxLounge|TechPro)\b/g;

  for (const message of chatHistory) {
    let match;
    while ((match = productNameRegex.exec(message.content)) !== null) {
      const productName = match[1];
      // Find products with this name and add their IDs
      const matchingProducts = allProducts.filter(p => p.name.includes(productName));
      mentionedProductIds.push(...matchingProducts.map(p => p.id));
    }
  }

  // Return unique product IDs
  return [...new Set(mentionedProductIds)];
};

// These functions are no longer used as we've replaced them with the productUtils module
// Keeping them commented out for reference if needed later
/*
const checkIfAskingForRecommendations = (message: string): boolean => {
  const lowercaseMessage = message.toLowerCase();

  // Keywords that indicate the user is asking for recommendations
  const recommendationKeywords = [
    'recommend', 'suggestion', 'suggest', 'looking for', 'show me',
    'what do you have', 'what products', 'best', 'top', 'popular',
    'interested in', 'want to buy', 'shopping for', 'search for',
    'find', 'need a', 'need some', 'good', 'great', 'quality'
  ];

  // Check if any of the recommendation keywords are in the message
  return recommendationKeywords.some(keyword => lowercaseMessage.includes(keyword));
};

const extractProductQueryInfo = (message: string): {
  category: string | null;
  keywords: string[];
  productTypes: string[];
  exactProductType: string | null;
} => {
  // Implementation removed as we're now using productUtils
  return {
    category: null,
    keywords: [],
    productTypes: [],
    exactProductType: null
  };
};
*/

// Function to get product information for the AI - simplified version that uses our new utilities
export const getProductInfo = (category?: string, tag?: string, query?: string): string => {
  let products: Product[] = [];

  if (category) {
    // Use our new utility to find products by category
    products = getRecommendedProducts(`category:${category}`, { limit: 5 });
  } else if (tag) {
    // Use our new utility to find products by tag
    products = getRecommendedProducts(`tag:${tag}`, { limit: 5 });
  } else if (query) {
    // Use our new utility to find products by query
    products = getRecommendedProducts(query, { limit: 5 });
  } else {
    // Return a sample of products if no filters provided
    products = allProducts.slice(0, 5);
  }

  if (products.length === 0) {
    return "No products found matching the criteria.";
  }

  // Use our shared formatting function
  return formatProductsForAI(products);
};
