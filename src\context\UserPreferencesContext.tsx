import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { UserPreferences } from '../utils/productUtils';
import { allProducts } from '../data/products';
import type { Product } from '../types/product';

// Default user preferences
const defaultUserPreferences: UserPreferences = {
  preferredCategories: [],
  preferredFeatures: [],
  dislikedFeatures: [],
  recentSearches: [],
  viewedProducts: [],
  selectedProducts: []
};

// Context interface
interface UserPreferencesContextType {
  userPreferences: UserPreferences;
  previousQueries: string[];
  updatePreferredCategories: (categories: string[]) => void;
  updatePriceRange: (range: { min?: number; max?: number }) => void;
  addPreferredFeature: (feature: string) => void;
  removePreferredFeature: (feature: string) => void;
  addDislikedFeature: (feature: string) => void;
  removeDislikedFeature: (feature: string) => void;
  updateMinRating: (rating: number) => void;
  addRecentSearch: (query: string) => void;
  addViewedProduct: (productId: string) => void;
  addSelectedProduct: (productId: string, selectionContext: string) => void;
  getSelectedProductById: (productId: string) => Product | null;
  getSelectedProductsByCategory: (category: string) => Product[];
  getLastSelectedProduct: () => Product | null;
  clearPreferences: () => void;
}

// Create context
const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

// Provider component
interface UserPreferencesProviderProps {
  children: ReactNode;
}

export const UserPreferencesProvider: React.FC<UserPreferencesProviderProps> = ({ children }) => {
  // State for user preferences
  const [userPreferences, setUserPreferences] = useState<UserPreferences>(() => {
    // Try to load preferences from localStorage
    const savedPreferences = localStorage.getItem('userPreferences');
    return savedPreferences ? JSON.parse(savedPreferences) : defaultUserPreferences;
  });

  // State for previous queries
  const [previousQueries, setPreviousQueries] = useState<string[]>([]);

  // Save preferences to localStorage when they change
  useEffect(() => {
    localStorage.setItem('userPreferences', JSON.stringify(userPreferences));
  }, [userPreferences]);

  // Update preferred categories
  const updatePreferredCategories = (categories: string[]) => {
    setUserPreferences(prev => ({
      ...prev,
      preferredCategories: categories
    }));
  };

  // Update price range
  const updatePriceRange = (range: { min?: number; max?: number }) => {
    setUserPreferences(prev => ({
      ...prev,
      priceRange: range
    }));
  };

  // Add preferred feature
  const addPreferredFeature = (feature: string) => {
    setUserPreferences(prev => {
      // Only add if not already in the list
      if (!prev.preferredFeatures.includes(feature)) {
        return {
          ...prev,
          preferredFeatures: [...prev.preferredFeatures, feature]
        };
      }
      return prev;
    });
  };

  // Remove preferred feature
  const removePreferredFeature = (feature: string) => {
    setUserPreferences(prev => ({
      ...prev,
      preferredFeatures: prev.preferredFeatures.filter(f => f !== feature)
    }));
  };

  // Add disliked feature
  const addDislikedFeature = (feature: string) => {
    setUserPreferences(prev => {
      // Only add if not already in the list
      if (!prev.dislikedFeatures.includes(feature)) {
        return {
          ...prev,
          dislikedFeatures: [...prev.dislikedFeatures, feature]
        };
      }
      return prev;
    });
  };

  // Remove disliked feature
  const removeDislikedFeature = (feature: string) => {
    setUserPreferences(prev => ({
      ...prev,
      dislikedFeatures: prev.dislikedFeatures.filter(f => f !== feature)
    }));
  };

  // Update minimum rating
  const updateMinRating = (rating: number) => {
    setUserPreferences(prev => ({
      ...prev,
      minRating: rating
    }));
  };

  // Add recent search
  const addRecentSearch = (query: string) => {
    // Add to previous queries for context
    setPreviousQueries(prev => {
      const newQueries = [query, ...prev];
      // Keep only the last 5 queries
      return newQueries.slice(0, 5);
    });

    // Add to recent searches in preferences
    setUserPreferences(prev => {
      const newSearches = [query, ...prev.recentSearches];
      // Keep only the last 10 searches
      return {
        ...prev,
        recentSearches: newSearches.slice(0, 10),
        lastQuery: query
      };
    });
  };

  // Add viewed product
  const addViewedProduct = (productId: string) => {
    setUserPreferences(prev => {
      // Only add if not already in the list
      if (!prev.viewedProducts.includes(productId)) {
        const newViewedProducts = [productId, ...prev.viewedProducts];
        // Keep only the last 20 viewed products
        return {
          ...prev,
          viewedProducts: newViewedProducts.slice(0, 20)
        };
      }
      return prev;
    });
  };

  // Add selected product with context
  const addSelectedProduct = (productId: string, selectionContext: string) => {
    // Find the product in the catalog
    const product = allProducts.find(p => p.id === productId);
    if (!product) return; // Product not found

    setUserPreferences(prev => {
      // Check if this product is already selected
      const existingIndex = prev.selectedProducts.findIndex(p => p.id === productId);

      let newSelectedProducts;
      if (existingIndex >= 0) {
        // Update existing selection with new context and timestamp
        newSelectedProducts = [...prev.selectedProducts];
        newSelectedProducts[existingIndex] = {
          id: productId,
          name: product.name,
          category: product.category,
          selectionTime: Date.now(),
          selectionContext
        };
      } else {
        // Add new selection
        newSelectedProducts = [
          {
            id: productId,
            name: product.name,
            category: product.category,
            selectionTime: Date.now(),
            selectionContext
          },
          ...prev.selectedProducts
        ];
      }

      // Keep only the last 10 selected products
      return {
        ...prev,
        selectedProducts: newSelectedProducts.slice(0, 10),
        // Also add to viewed products
        viewedProducts: prev.viewedProducts.includes(productId)
          ? prev.viewedProducts
          : [productId, ...prev.viewedProducts].slice(0, 20)
      };
    });
  };

  // Get a selected product by ID
  const getSelectedProductById = (productId: string): Product | null => {
    const selectedProduct = userPreferences.selectedProducts.find(p => p.id === productId);
    if (!selectedProduct) return null;

    return allProducts.find(p => p.id === productId) || null;
  };

  // Get selected products by category
  const getSelectedProductsByCategory = (category: string): Product[] => {
    const categoryProducts = userPreferences.selectedProducts
      .filter(p => p.category.toLowerCase() === category.toLowerCase())
      .map(p => p.id);

    return allProducts.filter(p => categoryProducts.includes(p.id));
  };

  // Get the most recently selected product
  const getLastSelectedProduct = (): Product | null => {
    if (userPreferences.selectedProducts.length === 0) return null;

    // Sort by selection time (descending) and get the first one
    const mostRecent = [...userPreferences.selectedProducts]
      .sort((a, b) => b.selectionTime - a.selectionTime)[0];

    return allProducts.find(p => p.id === mostRecent.id) || null;
  };

  // Clear all preferences
  const clearPreferences = () => {
    setUserPreferences(defaultUserPreferences);
    setPreviousQueries([]);
  };

  return (
    <UserPreferencesContext.Provider
      value={{
        userPreferences,
        previousQueries,
        updatePreferredCategories,
        updatePriceRange,
        addPreferredFeature,
        removePreferredFeature,
        addDislikedFeature,
        removeDislikedFeature,
        updateMinRating,
        addRecentSearch,
        addViewedProduct,
        addSelectedProduct,
        getSelectedProductById,
        getSelectedProductsByCategory,
        getLastSelectedProduct,
        clearPreferences
      }}
    >
      {children}
    </UserPreferencesContext.Provider>
  );
};

// Custom hook to use the user preferences context
export const useUserPreferences = (): UserPreferencesContextType => {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};

export default UserPreferencesContext;
