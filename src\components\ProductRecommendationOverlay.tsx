import React, { useEffect } from 'react';
import { useOverlay } from '../context/OverlayContext';
import type { SortCriteria } from '../context/OverlayContext';
import ProductCard from './ProductCard';
import './ProductRecommendationOverlay.css';

const ProductRecommendationOverlay: React.FC = () => {
  const {
    isOverlayVisible,
    recommendationTitle,
    hideOverlay,
    sortedProducts,
    sortCriteria,
    sortOrder,
    setSorting
  } = useOverlay();

  // Add body class for overlay state to enable targeted styling
  useEffect(() => {
    if (isOverlayVisible) {
      document.body.classList.add('product-overlay-active');
    } else {
      document.body.classList.remove('product-overlay-active');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('product-overlay-active');
    };
  }, [isOverlayVisible]);

  if (!isOverlayVisible) {
    return null;
  }

  // Handle sort criteria change
  const handleSortChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    setSorting(event.target.value as SortCriteria, sortOrder);
  };

  // Handle sort order change
  const handleOrderChange = () => {
    setSorting(sortCriteria, sortOrder === 'asc' ? 'desc' : 'asc');
  };

  return (
    <div className="product-recommendation-overlay">
      <div className="overlay-content">
        <div className="overlay-header">
          <h2>{recommendationTitle}</h2>
          <button className="close-button" onClick={hideOverlay}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </button>
        </div>

        <div className="overlay-body">
          {sortedProducts.length === 0 ? (
            <div className="no-recommendations">
              <p>No product recommendations available at the moment.</p>
            </div>
          ) : (
            <>
              <div className="sorting-controls">
                <div className="sort-container">
                  <label htmlFor="sort-criteria">Sort by:</label>
                  <select
                    id="sort-criteria"
                    value={sortCriteria}
                    onChange={handleSortChange}
                    className="sort-select"
                  >
                    <option value="relevance">Relevance</option>
                    <option value="price">Price</option>
                    <option value="rating">Rating</option>
                    <option value="name">Name</option>
                  </select>

                <button
                  className="sort-order-button"
                  onClick={handleOrderChange}
                  title={sortOrder === 'asc' ? 'Ascending' : 'Descending'}
                >
                  {sortOrder === 'asc' ? (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 5L12 19" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M19 12L12 19L5 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M12 19L12 5" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      <path d="M5 12L12 5L19 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  )}
                </button>
              </div>
              <div className="results-count">
                {sortedProducts.length} {sortedProducts.length === 1 ? 'product' : 'products'} found
              </div>
            </div>

            <div className="recommendation-grid">
              {sortedProducts.map((product, index) => (
                <div
                  key={product.id}
                  style={{ '--card-index': index } as React.CSSProperties}
                >
                  <ProductCard product={product} />
                </div>
              ))}
            </div>
          </>
        )}
        </div>
      </div>
    </div>
  );
};

export default ProductRecommendationOverlay;
