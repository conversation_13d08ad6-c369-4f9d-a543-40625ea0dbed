import { useState } from 'react';
import { useCart } from '../context/CartContext';
import type { Product } from '../types/product';

interface ProductCardProps {
  product: Product;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const [imageError, setImageError] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const { addItem, isInCart, getCartItem } = useCart();
  const fallbackImageUrl = 'https://via.placeholder.com/300x200/f0f4ff/4a6cf7?text=Premium+Product';

  // Handle adding item to cart
  const handleAddToCart = async () => {
    if (!product.inStock || isAddingToCart) return;

    setIsAddingToCart(true);
    try {
      const success = await addItem(product.id, 1);
      if (!success) {
        // Handle error - could show a toast notification
        console.error('Failed to add item to cart');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  // Check if item is already in cart
  const itemInCart = isInCart(product.id);
  const cartItem = getCartItem(product.id);

  return (
    <div className="product-card">
      <div className="product-image">
        <img
          src={imageError ? fallbackImageUrl : product.imageUrl}
          alt={product.name}
          onError={() => setImageError(true)}
        />
      </div>
      <div className="product-info">
        <h3>{product.name}</h3>
        <p className="product-price">${product.price.toFixed(2)}</p>
        <p className="product-description">{product.description.substring(0, 100)}...</p>
        <div className="product-meta">
          <span className="product-rating">★ {product.rating.toFixed(1)}</span>
          {product.inStock ? (
            <span className="product-stock in-stock">In Stock</span>
          ) : (
            <span className="product-stock out-of-stock">Out of Stock</span>
          )}
        </div>
        <button
          className={`btn-secondary ${itemInCart ? 'in-cart' : ''} ${!product.inStock ? 'disabled' : ''}`}
          onClick={handleAddToCart}
          disabled={!product.inStock || isAddingToCart}
        >
          {isAddingToCart ? (
            <span className="button-loading">
              <div className="loading-spinner small"></div>
              Adding...
            </span>
          ) : itemInCart ? (
            <span className="button-in-cart">
              ✓ In Cart ({cartItem?.quantity})
            </span>
          ) : !product.inStock ? (
            'Out of Stock'
          ) : (
            'Add to Cart'
          )}
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
